import 'package:flutter/material.dart';
import 'package:fujimo/global/precision_ruler_widget.dart';
import 'package:fujimo/global/capsule_ruler_widget.dart';

void main() {
  runApp(RulerDemoApp());
}

class RulerDemoApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Ruler Demo - Container Ticks',
      home: RulerDemoScreen(),
    );
  }
}

class RulerDemoScreen extends StatefulWidget {
  @override
  _RulerDemoScreenState createState() => _RulerDemoScreenState();
}

class _RulerDemoScreenState extends State<RulerDemoScreen> {
  double _precisionValue = 0.0;
  double _capsuleValue = 0.0;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Ruler Demo - Both Use Container Ticks'),
        backgroundColor: Colors.blue,
      ),
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'PrecisionRuler (Now uses containers like CapsuleRuler):',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
            Text(
              'Value: ${_precisionValue.toStringAsFixed(1)}',
              style: TextStyle(fontSize: 14, color: Colors.blue),
            ),
            SizedBox(height: 10),
            Container(
              height: 60,
              child: PrecisionRuler(
                value: _precisionValue,
                minValue: -10.0,
                maxValue: 10.0,
                tickColor: Colors.white,
                backgroundColor: Colors.black,
                tickBorderColor: Colors.grey,
                tickBorderWidth: 1.0,
                tickBorderRadius: 2.0,
                majorTickWidth: 3.0,
                mediumTickWidth: 2.0,
                minorTickWidth: 1.0,
                onChanged: (value) {
                  setState(() {
                    _precisionValue = value;
                  });
                },
              ),
            ),
            
            SizedBox(height: 40),
            
            Text(
              'CapsuleRuler (Already uses containers):',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
            Text(
              'Value: ${_capsuleValue.toStringAsFixed(1)}',
              style: TextStyle(fontSize: 14, color: Colors.amber),
            ),
            SizedBox(height: 10),
            Container(
              height: 60,
              child: CapsuleRuler(
                value: _capsuleValue,
                minValue: -10.0,
                maxValue: 10.0,
                tickColor: Colors.white,
                backgroundColor: Colors.black,
                tickBorderColor: Colors.grey,
                tickBorderWidth: 1.0,
                tickBorderRadius: 2.0,
                majorTickWidth: 3.0,
                mediumTickWidth: 2.0,
                minorTickWidth: 1.0,
                capsuleColor: Colors.amber,
                onChanged: (value) {
                  setState(() {
                    _capsuleValue = value;
                  });
                },
              ),
            ),
            
            SizedBox(height: 40),
            
            Container(
              padding: EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.green.shade50,
                border: Border.all(color: Colors.green),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '✅ Changes Completed:',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.green.shade800,
                    ),
                  ),
                  SizedBox(height: 8),
                  Text(
                    '• PrecisionRuler now uses RulerWidget with Container widgets for ticks\n'
                    '• Both rulers have consistent rendering approach\n'
                    '• Better support for tick styling (borders, radius, etc.)\n'
                    '• Improved visual consistency between widgets',
                    style: TextStyle(fontSize: 14),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
