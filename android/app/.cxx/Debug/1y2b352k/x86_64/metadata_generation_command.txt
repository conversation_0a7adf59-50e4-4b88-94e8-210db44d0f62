                        -H/opt/flutter/packages/flutter_tools/gradle/src/main/groovy
-DCMA<PERSON>_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=24
-DANDROID_PLATFORM=android-24
-DANDROID_ABI=x86_64
-DCMAKE_ANDROID_ARCH_ABI=x86_64
-DANDROID_NDK=/home/<USER>/Android/Sdk/ndk/28.0.12433566
-DCMAKE_ANDROID_NDK=/home/<USER>/Android/Sdk/ndk/28.0.12433566
-DCMAKE_TOOLCHAIN_FILE=/home/<USER>/Android/Sdk/ndk/28.0.12433566/build/cmake/android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=/home/<USER>/Android/Sdk/cmake/3.22.1/bin/ninja
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/home/<USER>/Dev/ExperimentalProjects/fujimo/build/app/intermediates/cxx/Debug/1y2b352k/obj/x86_64
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/home/<USER>/Dev/ExperimentalProjects/fujimo/build/app/intermediates/cxx/Debug/1y2b352k/obj/x86_64
-DCMAKE_BUILD_TYPE=Debug
-B/home/<USER>/Dev/ExperimentalProjects/fujimo/android/app/.cxx/Debug/1y2b352k/x86_64
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2