{"buildFiles": ["/opt/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/home/<USER>/Android/Sdk/cmake/3.22.1/bin/ninja", "-C", "/home/<USER>/Dev/ExperimentalProjects/fujimo/android/app/.cxx/Debug/1y2b352k/armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["/home/<USER>/Android/Sdk/cmake/3.22.1/bin/ninja", "-C", "/home/<USER>/Dev/ExperimentalProjects/fujimo/android/app/.cxx/Debug/1y2b352k/armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}, "toolchains": {"toolchain": {"cCompilerExecutable": "/home/<USER>/Android/Sdk/ndk/28.0.12433566/toolchains/llvm/prebuilt/linux-x86_64/bin/clang.lld", "cppCompilerExecutable": "/home/<USER>/Android/Sdk/ndk/28.0.12433566/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++.lld"}}, "cFileExtensions": [], "cppFileExtensions": []}