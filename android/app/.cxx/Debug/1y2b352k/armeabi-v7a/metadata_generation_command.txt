                        -H/opt/flutter/packages/flutter_tools/gradle/src/main/groovy
-DC<PERSON><PERSON>_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=24
-DANDROID_PLATFORM=android-24
-DANDROID_ABI=armeabi-v7a
-DCMAKE_ANDROID_ARCH_ABI=armeabi-v7a
-DANDROID_NDK=/home/<USER>/Android/Sdk/ndk/28.0.12433566
-DCMAKE_ANDROID_NDK=/home/<USER>/Android/Sdk/ndk/28.0.12433566
-DCMAKE_TOOLCHAIN_FILE=/home/<USER>/Android/Sdk/ndk/28.0.12433566/build/cmake/android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=/home/<USER>/Android/Sdk/cmake/3.22.1/bin/ninja
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/home/<USER>/Dev/ExperimentalProjects/fujimo/build/app/intermediates/cxx/Debug/1y2b352k/obj/armeabi-v7a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/home/<USER>/Dev/ExperimentalProjects/fujimo/build/app/intermediates/cxx/Debug/1y2b352k/obj/armeabi-v7a
-DCMAKE_BUILD_TYPE=Debug
-B/home/<USER>/Dev/ExperimentalProjects/fujimo/android/app/.cxx/Debug/1y2b352k/armeabi-v7a
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2