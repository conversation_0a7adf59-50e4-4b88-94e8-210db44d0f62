import 'dart:async';

import 'package:battery_plus/battery_plus.dart';

class BatteryService {
  final Battery _battery = Battery();
  Timer? _batteryLevelTimer;

  final _batteryLevelController = StreamController<int>();
  Stream<int> get batteryLevelStream => _batteryLevelController.stream;

  Future<void> initBattery() async {
    //*TBD use alternative method of listening to battery direcly
    // _batteryLevelTimer = Timer.periodic(const Duration(seconds: 5), (_) async {
    //   final level = await _battery.batteryLevel;
    //   _batteryLevelController.add(level);
    // });

    
  }

  void dispose() {
    _batteryLevelTimer?.cancel();
    _batteryLevelController.close();
  }
}
