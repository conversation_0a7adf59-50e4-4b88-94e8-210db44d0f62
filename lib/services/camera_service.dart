import 'dart:io';

import 'package:camera/camera.dart';
import 'package:dartz/dartz.dart';
import 'package:image/image.dart' as img;
import 'package:path_provider/path_provider.dart';

enum CameraFlashMode {
  auto,
  off,
  always,
  torch;

  FlashMode toFlashMode() {
    switch (this) {
      case CameraFlashMode.auto:
        return FlashMode.auto;
      case CameraFlashMode.off:
        return FlashMode.off;
      case CameraFlashMode.always:
        return FlashMode.always;
      case CameraFlashMode.torch:
        return FlashMode.torch;
    }
  }

  static CameraFlashMode fromFlashMode(FlashMode mode) {
    switch (mode) {
      case FlashMode.auto:
        return CameraFlashMode.auto;
      case FlashMode.off:
        return CameraFlashMode.off;
      case FlashMode.always:
        return CameraFlashMode.always;
      case FlashMode.torch:
        return CameraFlashMode.torch;
    }
  }
}

enum SelectedCamera {
  front,
  back;
}

class CameraService {
  late final CameraController _cameraController;
  late final List<CameraDescription> _cameras;

  SelectedCamera _selectedCamera = SelectedCamera.back;
  bool _flashOnOff = false;
  bool _isIntialized = false;
  CameraFlashMode _flashMode = CameraFlashMode.off;

  SelectedCamera get selectedCamera => _selectedCamera;
  bool get flashOnOff => _flashOnOff;
  bool get isInitialized => _isIntialized;
  CameraController get cameraController => _cameraController;
  CameraFlashMode get flashMode => _flashMode;

  Future<void> initializeCamera() async {
    try {
      _cameras = await availableCameras();
      _cameraController = CameraController(
        _cameras[_selectedCamera == SelectedCamera.back ? 0 : 1],
        ResolutionPreset.low,
      );
      await _cameraController.initialize();
      await _cameraController.setFlashMode(FlashMode.off);
      await _cameraController.setZoomLevel(0.0);

      _cameraController.startImageStream((CameraImage image) {
        // Process the image data here
      });

      _isIntialized = true;
    } catch (e) {
      if (e is CameraException) {
        _isIntialized = false;
      }
    }
  }

  Future<void> switchCamera() async {
    _selectedCamera = _selectedCamera == SelectedCamera.back
        ? SelectedCamera.front
        : SelectedCamera.back;

    if (_selectedCamera == SelectedCamera.front) {
      _flashOnOff = false;
      _flashMode = CameraFlashMode.off;
    }

    await _cameraController.setDescription(
      _cameras[_selectedCamera == SelectedCamera.back ? 0 : 1],
    );
  }

  Future<void> toggleFlashMode() async {
    if (_cameras[_selectedCamera == SelectedCamera.back ? 0 : 1]
            .lensDirection ==
        CameraLensDirection.back) {
      switch (_flashMode) {
        case CameraFlashMode.auto:
          _flashMode = CameraFlashMode.off;
          _flashOnOff = false;
        case CameraFlashMode.off:
          _flashMode = CameraFlashMode.always;
          _flashOnOff = true;
        case CameraFlashMode.always:
          _flashMode = CameraFlashMode.torch;
          _flashOnOff = true;
        case CameraFlashMode.torch:
          _flashMode = CameraFlashMode.auto;
          _flashOnOff = true;
      }
      await _cameraController.setFlashMode(_flashMode.toFlashMode());
    } else {
      _flashMode = CameraFlashMode.off;
      _flashOnOff = false;
      await _cameraController.setFlashMode(_flashMode.toFlashMode());
    }
  }

  Future<Either<CameraException, XFile>> takePicture() async {
    try {
      final XFile picture = await _cameraController.takePicture();
      final bool isFrontCamera = _selectedCamera == SelectedCamera.front;
      final XFile processedPicture =
          await _processAndSaveImage(picture, isFrontCamera);

      return Right(processedPicture);
    } catch (e) {
      if (e is CameraException) {
        return Left(e);
      } else {
        return Left(
          CameraException(
            'UnknownError',
            'An unknown error occurred: $e',
          ),
        );
      }
    }
  }

  Future<XFile> _processAndSaveImage(XFile picture, bool isFrontCamera) async {
    final bytes = await picture.readAsBytes();
    final originalImage = img.decodeImage(bytes);

    if (originalImage == null) {
      return picture; // Return the original picture if decoding fails
    }

    img.Image processedImage = originalImage;

    // If front camera, flip horizontally
    if (isFrontCamera) {
      processedImage = img.flipHorizontal(processedImage);
    }

    // Encode the processed image (flipped or original)
    final processedBytes = img.encodeJpg(processedImage);

    // Save the processed image to a temporary file
    final tempDir = await getTemporaryDirectory();
    final processedPath =
        '${tempDir.path}/${DateTime.now().millisecondsSinceEpoch}_processed.jpg';
    final processedFile = File(processedPath);
    await processedFile.writeAsBytes(processedBytes);

    // If it's the back camera, save it just like the front camera
    return XFile(processedFile.path);
  }

  Future<void> setZoom(double level) async {
    const normalZoom = 1.0;
    final maxZoomLevel = await _cameraController.getMaxZoomLevel();

    final zoomRange = maxZoomLevel - normalZoom;
    final zoomLevel = normalZoom + (zoomRange * (level / 100));

    await _cameraController.setZoomLevel(zoomLevel);
  }

  void dispose() {
    _cameraController.dispose();
  }
}
