import 'dart:developer';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';

class AppLifecycleObserver extends WidgetsBindingObserver {
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.paused) {
      cleanTempFiles();
    }

    super.didChangeAppLifecycleState(state);
  }

  Future<void> cleanTempFiles() async {
    final tempDir = await getTemporaryDirectory();
    final tempFiles = tempDir.listSync();

    for (var i = 0; i < tempFiles.length; i++) {
      if (tempFiles[i] is File && tempFiles[i].path.endsWith('.jpg')) {
        try {
          log('Successfully delete file: ${tempFiles[i].path}');

          await tempFiles[i].delete();
        } catch (e) {
          log('Failed to delete file: ${tempFiles[i].path}');
        }
      }
    }
  }
}
