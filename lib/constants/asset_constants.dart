class AssetConstants {
  // Camera Components
  static const String cameraComponentsPath = 'assets/camera-components';
  static const String cameraSurface = '$cameraComponentsPath/camera_surface.png';
  static const String cameraButton = '$cameraComponentsPath/camera_button.png';
  static const String filterWindow = '$cameraComponentsPath/filter-window.png';
  static const String imageWindow = '$cameraComponentsPath/image-window.png';
  static const String flash = '$cameraComponentsPath/flash.png';
  static const String rotate = '$cameraComponentsPath/rotate.png';
  static const String cameraDial = '$cameraComponentsPath/camera_dial.png';
  static const String cameraIndicator = '$cameraComponentsPath/camera_indicator.png';
  static const String cameraScreenSurface = '$cameraComponentsPath/camera_screen_surface.png';
  static const String buttonOption = '$cameraComponentsPath/button-option.png';
  static const String layer1 = '$cameraComponentsPath/layer1.png';
  static const String layer2 = '$cameraComponentsPath/layer2.png';
  static const String layer3 = '$cameraComponentsPath/layer3.png';
  static const String layer4 = '$cameraComponentsPath/layer4.png';
  static const String layer5 = '$cameraComponentsPath/layer5.png';
  static const String layer6 = '$cameraComponentsPath/layer6.png';
  static const String layer7 = '$cameraComponentsPath/layer7.png';
  static const String batteryLevel1 = '$cameraComponentsPath/battery1.png';
  static const String batteryLevel2 = '$cameraComponentsPath/battery2.png';
  static const String batteryLevel3 = '$cameraComponentsPath/battery3.png';
  static const String batteryLevel4 = '$cameraComponentsPath/battery4.png';
  static const String batteryLevel5 = '$cameraComponentsPath/battery5.png';

  
  static const String indicatorOff = '$cameraComponentsPath/indicator-off.png';
  static const String indicatorGreen = '$cameraComponentsPath/indicator-green.png';
  static const String indicatorBlue = '$cameraComponentsPath/indicator-blue.png';
  static const String indicatorYellow = '$cameraComponentsPath/indicator-yellow.png';
  static const String indicatorRed = '$cameraComponentsPath/indicator-red.png';

  static const String cameraRoll = '$cameraComponentsPath/camera-roll.png';
  static const String crop = '$cameraComponentsPath/crop.png';
  static const String focus = '$cameraComponentsPath/focus.png';
  static const String gallery = '$cameraComponentsPath/gallery.png';
  static const String grid = '$cameraComponentsPath/grid.png';
  static const String iso = '$cameraComponentsPath/iso.png';
  static const String settings = '$cameraComponentsPath/setting.png';
  static const String nightMode = '$cameraComponentsPath/night-mode.png';
  static const String stopWatch = '$cameraComponentsPath/stopwatch.png';
  static const String photoCamera = '$cameraComponentsPath/photo-camera.png';
  static const String shutter = '$cameraComponentsPath/shutter.png';
  static const String switchCamera = '$cameraComponentsPath/switch-camera.png';





  static const String indicatorRedOff = '$cameraComponentsPath/indicator-red-off.png';
  static const String indicatorRedOn = '$cameraComponentsPath/indicator-red-on.png';


  static const String silverButtonRing = '$cameraComponentsPath/silver-button-ring.png';
  static const String silverButton = '$cameraComponentsPath/silver-button.png';

  static const String silverButtonRingDark = '$cameraComponentsPath/silver-button-ring-dark.png';
  static const String silverButtonDark = '$cameraComponentsPath/silver-button-dark.png';
  
  static const String silverButtonRingShadow = '$cameraComponentsPath/silver-button-ring-shadow.png';
  static const String silverButtonShadow = '$cameraComponentsPath/silver-button-shadow.png';
  
  static const String miniMetalBar = '$cameraComponentsPath/metal-bar1.png';
  static const String largeMetalBar = '$cameraComponentsPath/metal-bar2.png';
  
  // Shadow placeholders
  static const String filmPlaceShadow = '$cameraComponentsPath/film-place-shadow.png';
  static const String imagePlaceShadow = '$cameraComponentsPath/image-place-shadow.png';
  static const String cameraButtonPlaceShadow = '$cameraComponentsPath/camera-button-place-shadow.png';
  static const String focusLarge = '$cameraComponentsPath/focus-large.png';
  // Audio
  static const String rotatingSwitch = 'audio/rotating-switch.wav';
  static const String buttonPress = 'audio/button-press.wav';
  static const String buttonPressBeep = 'audio/button-press-beep.wav';
  static const String imageCaptured = 'audio/image-captured.wav';
  static const String clickEffect = 'audio/click-effect.wav';
}