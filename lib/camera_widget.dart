import 'dart:io';

import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:fujimo/constants/asset_constants.dart';
import 'package:fujimo/widgets/camera_button.dart';
import 'package:fujimo/widgets/camera_controls.dart';
import 'package:fujimo/widgets/camera_digital_display_widget.dart';
import 'package:fujimo/widgets/camera_preview_container.dart';
import 'package:fujimo/widgets/camera_scroll_wheel_widget.dart';
import 'package:fujimo/widgets/camera_surface_widget.dart';
import 'package:fujimo/widgets/captured_image_window.dart';
import 'package:fujimo/controllers/camera-cubit/camera_cubit.dart';

class CameraWidget extends StatefulWidget {
  const CameraWidget({
    super.key,
  });

  @override
  State<CameraWidget> createState() => _CameraWidgetState();
}

class _CameraWidgetState extends State<CameraWidget> {
  late AudioPlayer player;

  @override
  void initState() {
    super.initState();
    _initializeState();
  }

  void _initializeState() {
    player = AudioPlayer();
  }

  @override
  void dispose() {
    player.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          Expanded(
            child: _buildMainCameraSection(),
          ),
          _buildBottomSection(),
        ],
      ),
    );
  }

  Widget _buildMainCameraSection() {
    return CameraSurfaceWidget(
      child: SizedBox(
        width: double.infinity,
        height: double.infinity,
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    margin:
                        const EdgeInsets.only(top: 110, left: 10, bottom: 12),
                    width: double.infinity,
                    child: CameraControls(player: player),
                  ),
                  const Expanded(
                    child: CameraPreviewContainer(),
                  ),
                ],
              ),
            ),
            _buildControlsColumn(),
          ],
        ),
      ),
    );
  }

  Widget _buildControlsColumn() {
    // const backgroundColor = Color.fromARGB(255, 255, 135, 50);
    // const selectedColor = Color.fromARGB(255, 0, 0, 0);

    const backgroundColor = Color.fromARGB(255, 0, 0, 0);
    const selectedColor = Color.fromARGB(255, 224, 224, 224);

    // const backgroundColor = Color.fromARGB(255, 0, 0, 0);
    // const selectedColor = Color.fromARGB(255, 255, 136, 0);

    // const backgroundColor = Color.fromARGB(255, 28, 164, 242);
    // const selectedColor = Color.fromARGB(255, 21, 21, 21);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      mainAxisSize: MainAxisSize.max,
      children: [
        const Spacer(flex: 5),
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 5),
          child: RotatedBox(
            quarterTurns: 1,
            child: CameraDisplayWidget(
              width: 190,
              gradientColors: const [backgroundColor, backgroundColor],
              showBorder: false,
              showShadow: false,
              labelTextColor: selectedColor,
              activeColor: selectedColor,
              valueTextColor: selectedColor,
              inactiveDigitColor: selectedColor.withAlpha(40),
              iconColor: selectedColor,
              bracketColor: selectedColor,
              showGlowEffect: false,
              shadowBlurRadius: 0,
              padding: const EdgeInsets.symmetric(horizontal: 15),
              iso: "AUTO",
              aperture: "5.1",
            ),
          ),
        ),

        const Spacer(flex: 3),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 10),
          width: 55,
          child: const FittedBox(
            fit: BoxFit.contain,
            child: CameraScrollWheelWidget(),
          ),
        ),
        const Spacer(flex: 1),
      ],
    );
  }

  Widget _buildBottomSection() {
    //TODO: use Row for adding
    return SizedBox(
      width: double.infinity,
      height: MediaQuery.of(context).size.height * .25,
      child: Expanded(
        child: Stack(
          children: [
            Image.asset(AssetConstants.cameraSurface,
                fit: BoxFit.fill,
                width: double.infinity,
                height: double.infinity),
            Center(child: _buildBottomControls()),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomControls() {
    return FittedBox(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildFilterWindow(),
          const Center(
            child: CameraButton(),
          ),
          _buildCapturedImagePreview(),
        ],
      ),
    );
  }

  Widget _buildFilterWindow() {
    return const CameraFilmWindow(displayFilm: null);
  }

  Widget _buildCapturedImagePreview() {
    return BlocConsumer<CameraCubit, CameraState>(
      buildWhen: (previous, current) => current is PhotoCaptured,
      listener: (context, state) {
        if (state is PhotoCaptured) {
          player.stop();
          player.seek(Duration.zero);
          player.play(AssetSource(AssetConstants.imageCaptured));
        }
      },
      builder: (context, state) => CapturedImageWindow(
        displayImage:
            state is PhotoCaptured ? Image.file(File(state.image.path)) : null,
      ),
    );
  }
}

class CameraFilmWindow extends StatelessWidget {
  static const double _windowWidth = 120.0;
  static const double _windowHeight = 70.0;
  static const double _filmImageScale = 0.9;
  static const double _shadowOffset = 10.0;
  static const double _padding = 10;

  static final Widget _shadowWidget = Image.asset(
    AssetConstants.filmPlaceShadow,
    width: _windowWidth,
    height: _windowHeight,
  );

  // Cached window frame widget
  static final Widget _windowFrame = Image.asset(
    AssetConstants.filterWindow,
    width: _windowWidth,
    height: _windowHeight,
  );

  final Image? displayFilm;

  const CameraFilmWindow({
    super.key,
    required this.displayFilm,
  });

  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(
      child: RotatedBox(
        quarterTurns: 3,
        child: Padding(
          padding: const EdgeInsets.all(_padding),
          child: SizedBox(
            width: _windowWidth + _shadowOffset,
            height: _windowHeight + _shadowOffset,
            child: Stack(
              alignment: Alignment.center,
              children: [
                Center(
                  child: SizedBox(
                    width: _windowWidth + _shadowOffset,
                    height: _windowHeight + _shadowOffset,
                    child: FittedBox(
                      child: _shadowWidget,
                    ),
                  ),
                ),
                Center(
                  child: SizedBox(
                    width: _windowWidth * _filmImageScale,
                    height: _windowWidth * _filmImageScale,
                    child: _buildImageWidget(),
                  ),
                ),
                Center(child: _windowFrame),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildImageWidget() {
    if (displayFilm == null) {
      return Container(color: Colors.transparent);
    }

    return ClipRRect(
      child: SizedBox.expand(
        child: FittedBox(
          fit: BoxFit.cover,
          child: displayFilm!,
        ),
      ),
    );
  }
}
