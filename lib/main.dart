import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:fujimo/controllers/battery-cubit/battery_cubit.dart';
import 'package:fujimo/controllers/camera-cubit/camera_cubit.dart';
import 'package:fujimo/camera_widget.dart';
import 'package:fujimo/di/injector.dart';
import 'package:fujimo/utils/app_lifecycle_observer.dart';

/// Entry point of the application.
/// Initializes necessary services and configurations before running the app.
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  WidgetsBinding.instance.addObserver(AppLifecycleObserver());
  await SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual, overlays: []);
  await SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
  await initAppModules();

  runApp(
    MultiBlocProvider(
      providers: [
        BlocProvider<CameraCubit>(
          create: (context) => CameraCubit()
          //* Not using camera for now to avoid power consumption
          ..initializeCamera(),
        ),
        BlocProvider(create: (context) => BatteryCubit()
        //* Not using battery initialiazion for now to avoid power consumption
       // ..initializeBattery()
        )
      ],
      child: const MyApp(),
    ),
  );
}

/// Root widget of the application.
/// Configures the global theme and initial route.
class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(
          seedColor: Colors.deepPurple,
        ),
        // Disable Material 3 for consistent design across devices
        useMaterial3: false,
      ),
      //TODO: remove splash effect
      //TODO: remove ripple effect
      // Set camera as the initial screen
      home: const CameraWidget(),
    );
  }
}
