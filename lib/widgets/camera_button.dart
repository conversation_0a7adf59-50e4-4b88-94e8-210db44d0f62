import 'dart:async';
import 'dart:math' as math;

import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:fujimo/controllers/camera-cubit/camera_cubit.dart';
import 'package:fujimo/constants/asset_constants.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class CameraButton extends StatefulWidget {
  const CameraButton({
    super.key,
  });

  @override
  CameraButtonState createState() => CameraButtonState();
}

class CameraButtonState extends State<CameraButton>
    with SingleTickerProviderStateMixin {
  Offset? _previousPosition;
  // Use ValueNotifier for reactive state
  final ValueNotifier<double> _scale = ValueNotifier(1.0);
  final ValueNotifier<double> _rotation = ValueNotifier(0.0);
  final ValueNotifier<int> _remainingTime = ValueNotifier(0);
  final ValueNotifier<bool> _blink = ValueNotifier(false);

  late AnimationController _animationController;
  late AudioPlayer player;
  late Animation<double> _animation;
  int? _previousTick;
  int? _currentTick;
  Timer? _rotationTimer;
  Timer? _blinkTimer;

  final double startPosition = -90;
  final int dragTicks = 10;
  final Duration animationDuration = const Duration(milliseconds: 600);
  final Curve animationType = Curves.elasticInOut;
  final double spaceBetweenTicks = 25;

  // Add sensitivity multiplier (smaller number = slower rotation)
  final double _rotationSensitivity = 1.0; // Adjust this value between 0.1 and 1.0

  @override
  void initState() {
    super.initState();
    _rotation.value = startPosition * math.pi / 180;
    _initializeState();
  }

  void _initializeState() {
    player = AudioPlayer();
    _animationController = AnimationController(
      vsync: this,
      duration: animationDuration,
    );
    _initializeAnimation();
  }

  void _initializeAnimation() {
    _animation = Tween<double>(
      begin: _rotation.value,
      end: _rotation.value,
    ).animate(
      CurvedAnimation(parent: _animationController, curve: animationType),
    )..addListener(() {
        _rotation.value = _animation.value;
      });
  }

  @override
  void dispose() {
    _rotationTimer?.cancel();
    _blinkTimer?.cancel();
    _animationController.dispose();
    _scale.dispose();
    _rotation.dispose();
    _remainingTime.dispose();
    _blink.dispose();
    player.dispose();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    _scale.value = 0.95; // Scale down on press

    player.stop();
    player.seek(Duration.zero);
    player.play(AssetSource(AssetConstants.clickEffect));
  }

  void _onTapUp(TapUpDetails details) {
    _scale.value = 1.0; // Scale back up on release
  }

  void _onTapCancel() {
    _scale.value = 1.0; // Scale back up if press is canceled
  }

  void _onPanStart(DragStartDetails details) {
    _previousPosition = details.localPosition;
  }

  void _onPanUpdate(DragUpdateDetails details) {
    if (_previousPosition == null) {
      _previousPosition = details.localPosition;
      return;
    }

    final Offset center = Offset(
      (context.size?.width ?? 0) / 2,
      (context.size?.height ?? 0) / 2,
    );

    // Calculate angles
    final double previousAngle = math.atan2(
      _previousPosition!.dy - center.dy,
      _previousPosition!.dx - center.dx,
    );
    
    final double currentAngle = math.atan2(
      details.localPosition.dy - center.dy,
      details.localPosition.dx - center.dx,
    );

    // Calculate the difference in angles
    double deltaAngle = currentAngle - previousAngle;
    
    // Normalize the angle
    if (deltaAngle > math.pi) {
      deltaAngle -= 2 * math.pi;
    } else if (deltaAngle < -math.pi) {
      deltaAngle += 2 * math.pi;
    }

    // Apply sensitivity multiplier to deltaAngle
    deltaAngle *= _rotationSensitivity;

    final newRotation = (_rotation.value + deltaAngle).clamp(
      startPosition * math.pi / 180,
      (startPosition + dragTicks * spaceBetweenTicks) * math.pi / 180,
    );

    _rotation.value = newRotation;
    _currentTick = _calculateCurrentTick(newRotation);
    _remainingTime.value = _currentTick ?? 0;

    if (_previousTick != null && _previousTick != _currentTick) {
      _playRotationSound();
    }
    _previousTick = _currentTick;
    context.read<CameraCubit>().updateTimer(_currentTick!);
    
    _previousPosition = details.localPosition;
  }

  void _onPanEnd(DragEndDetails details) {
    _previousPosition = null;
    double newPosition = _calculateNearestPosition(_rotation.value);
    _currentTick = _calculateCurrentTick(_rotation.value);
    _previousTick = _currentTick;
    _animateToPosition(newPosition);
  }

  double _calculateNearestPosition(double rotation) {
    int newPosition =
        ((rotation * 180 / math.pi - startPosition) / spaceBetweenTicks)
            .round()
            .clamp(0, dragTicks);
    return (startPosition + newPosition * spaceBetweenTicks) * math.pi / 180;
  }

  int _calculateCurrentTick(double rotation) {
    final tick =
        ((rotation * 180 / math.pi - startPosition) / spaceBetweenTicks)
            .round()
            .clamp(0, dragTicks);

    return tick;
  }

  void _animateToPosition(double newPosition) {
    _animation =
        Tween<double>(begin: _rotation.value, end: newPosition).animate(
      CurvedAnimation(parent: _animationController, curve: animationType),
    );
    _animationController.reset();
    _animationController.forward();
  }

  void _playRotationSound() {
    try {
      player.stop();
      player.seek(Duration.zero);
      player.play(AssetSource(AssetConstants.rotatingSwitch));
    } catch (e) {
      // Handle or log error if needed
    }
  }

  void _startTimer(int seconds) {
    _remainingTime.value = seconds;
    _blink.value = false;
    _rotationTimer?.cancel();
    _blinkTimer?.cancel();

    // Start rotation timer
    _rotationTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_remainingTime.value > 0) {
        _remainingTime.value--;
        context.read<CameraCubit>().updateTimer(_remainingTime.value);
        _moveToPreviousTick();
      } else {
        _rotationTimer?.cancel();
        _blinkTimer?.cancel();
        context.read<CameraCubit>().takePicture();
        _blink.value = false;
      }
    });

    // Start blink timer with dynamic interval using recursive Timer
    void scheduleBlink() {
      _blinkTimer?.cancel();
      _blink.value = !_blink.value;
      final blinkInterval = _remainingTime.value < 3
          ? const Duration(milliseconds: 100)
          : const Duration(milliseconds: 500);

      _blinkTimer = Timer(blinkInterval, scheduleBlink);
    }

    if (_remainingTime.value >= 1) {
      scheduleBlink();
    } else {
      _blink.value = false;
    }
  }

  void _moveToPreviousTick() {
    int currentTick = _calculateCurrentTick(_rotation.value);
    int previousTick = (currentTick - 1).clamp(0, dragTicks);
    double endRotation =
        (startPosition + previousTick * spaceBetweenTicks) * math.pi / 180;
    _animateToPosition(endRotation);
    _playRotationSound();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onPanStart: _onPanStart,
      onPanUpdate: _onPanUpdate,
      onPanEnd: _onPanEnd,
      onTapDown: _onTapDown,
      onTapUp: _onTapUp,
      onTapCancel: _onTapCancel,
      excludeFromSemantics: true,
      onTap: () {
        _startTimer(_remainingTime.value);
      },
      child: SizedBox(
        height: 280 * .60,
        width: 280 * .60,
        child: ValueListenableBuilder<double>(
          valueListenable: _rotation,
          builder: (context, rotation, _) => Transform.rotate(
            angle: rotation,
            child: Stack(
              alignment: Alignment.center,
              children: [
                _buildShadowLayer(),
                _buildDialLayer(),
                _buildBorderLayer(),
                _buildIndicatorLayer(),
                _buildCenterButton(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildShadowLayer() => Image.asset(
        AssetConstants.cameraButtonPlaceShadow,
        width: 200 * .65,
        height: 200 * .65,
      );

  Widget _buildDialLayer() => Image.asset(
        AssetConstants.cameraDial,
        width: 200 * .60,
        height: 200 * .60,
      );

  Widget _buildBorderLayer() => Container(
        width: 150 * .60,
        height: 150 * .60,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          border: Border.all(
            width: 6,
            color: Colors.black,
          ),
        ),
      );

  Widget _buildIndicatorLayer() => Positioned(
        top: 0,
        child: Container(
          decoration: BoxDecoration(
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.6),
                blurRadius: 11,
                offset: const Offset(0, -7.25),
              ),
            ],
          ),
          child: ValueListenableBuilder2<int, bool>(
            firstValueListenable: _remainingTime,
            secondValueListenable: _blink,
            builder: (context, remainingTime, blink, _) => RepaintBoundary(
              child: CameraIndicatorWidget(
                remainingTime: remainingTime,
                blink: blink,
              ),
            ),
          ),
        ),
      );

  Widget _buildCenterButton() => Center(
        child: ValueListenableBuilder<double>(
          valueListenable: _scale,
          builder: (context, scale, _) => AnimatedScale(
            scale: scale,
            duration: const Duration(milliseconds: 70),
            child: Stack(
              children: [
                Container(
                  width: 150 * .60,
                  height: 150 * .60,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      width: 6,
                      color: Colors.black,
                    ),
                  ),
                  child: Center(
                    child: Image.asset(
                      AssetConstants.cameraButton,
                      width: 150 * .60,
                      height: 150 * .60,
                    ),
                  ),
                ),
                if (_scale.value < 1.0)
                  Container(
                    width: 150 * .60,
                    height: 150 * .60,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: Colors.black.withValues(alpha: 0.2),
                    ),
                  ),
              ],
            ),
          ),
        ),
      );
}

class CameraIndicatorWidget extends StatelessWidget {
  final int remainingTime;
  final bool blink;

  const CameraIndicatorWidget({
    super.key,
    required this.remainingTime,
    required this.blink,
  });

  @override
  Widget build(BuildContext context) {
    return Image.asset(
      blink ? AssetConstants.indicatorRedOn : AssetConstants.indicatorRedOff,
      width: 50 * .60,
    );
  }
}

// Helper class for listening to two ValueNotifier
class ValueListenableBuilder2<A, B> extends StatelessWidget {
  final ValueListenable<A> firstValueListenable;
  final ValueListenable<B> secondValueListenable;
  final Widget Function(BuildContext context, A first, B second, Widget? child)
      builder;
  final Widget? child;

  const ValueListenableBuilder2({
    super.key,
    required this.firstValueListenable,
    required this.secondValueListenable,
    required this.builder,
    this.child,
  });

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<A>(
      valueListenable: firstValueListenable,
      builder: (context, first, _) {
        return ValueListenableBuilder<B>(
          valueListenable: secondValueListenable,
          builder: (context, second, _) {
            return builder(context, first, second, child);
          },
        );
      },
    );
  }
}
