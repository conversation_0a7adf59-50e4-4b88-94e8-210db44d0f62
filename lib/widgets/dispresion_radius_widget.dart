import 'package:flutter/material.dart';
import 'package:fujimo/global/capsule_ruler_widget.dart';

//!BUG fix issue where this widget is not working
class DispresionRadiusWidget extends StatefulWidget {
  const DispresionRadiusWidget({super.key});

  @override
  State<DispresionRadiusWidget> createState() => _DispresionRadiusWidgetState();
}

class _DispresionRadiusWidgetState extends State<DispresionRadiusWidget> {
  double _value = 0.0;

  @override
  Widget build(BuildContext context) {
    return CapsuleRuler(
      maxValue: 100,
      minValue: 0,
      value: _value,
      height: 30,
      backgroundColor: Colors.black,
      tickColor: Colors.white,
      tickBorderRadius: 3.0,
      majorTickWidth: 2.0,
      minorTickHeightRatio: 0.5,

      mediumTickHeightRatio: 0.5,

      majorTickHeightRatio: 0.8,

      mediumTickWidth: 2.0,
      minorTickWidth: 2.0,

      capsuleColor: Colors.amber,
      capsuleBorderColor: Colors.white,
      capsuleBorderWidth: 0.8,
      capsuleBackgroundColor: Colors.amber.withAlpha(200),
      capsuleShadowColor: Colors.white.withAlpha(100),
      capsuleShadowElevation: 2.0,
      capsuleWidth: 5, // Narrower width for the full-height capsule
      cornerRadius: 1,
      //rulerPadding: 5,
      minorTicksPerMajor: 0,
      majorTickInterval: 10,
      enableSnapping: false,
      forceFullWidth: false,
      showLabels: false,

      autoAdaptToSpace: false,

      onChanged: (value) {
        setState(() {
          _value = value.clamp(0.0, 100.0);
        });
      },
    );
  }
}
