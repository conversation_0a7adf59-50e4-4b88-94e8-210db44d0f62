import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:fujimo/controllers/camera-cubit/camera_cubit.dart';
import 'package:fujimo/constants/asset_constants.dart';
import 'package:fujimo/services/camera_service.dart';

class CameraControls extends StatefulWidget {
  final AudioPlayer player;

  const CameraControls({
    super.key,
    required this.player,
  });

  @override
  State<CameraControls> createState() => _CameraControlsState();
}

class _CameraControlsState extends State<CameraControls> {
  late AudioPlayer player;

  @override
  void initState() {
    super.initState();
    _initializeState();
  }

  @override
  void dispose() {
    player.dispose();
    super.dispose();
  }

  void _initializeState() {
    player = AudioPlayer();
  }

  String _getFlashModeIndicator(CameraFlashMode mode) {
    switch (mode) {
      case CameraFlashMode.auto:
        return AssetConstants.indicatorBlue;
      case CameraFlashMode.off:
        return AssetConstants.indicatorOff;
      case CameraFlashMode.always:
        return AssetConstants.indicatorGreen;
      case CameraFlashMode.torch:
        return AssetConstants.indicatorRed;
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<CameraCubit, CameraState>(
      buildWhen: (previous, current) => current is CameraConfiguration,
      listener: (context, state) {},
      builder: (context, state) {
        final configs = state as CameraConfiguration;
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 5),
          child: Row(
            spacing: 18,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              ButtonGroup(
                player: player,
                icon: AssetConstants.flash,
                indicator: _getFlashModeIndicator(configs.flashMode),
                onPressed: context.read<CameraCubit>().toggleFlash,
              ),
              ButtonGroup(
                player: player,
                icon: AssetConstants.switchCamera,
                indicator: configs.selectedCamera == SelectedCamera.back
                    ? AssetConstants.indicatorOff
                    : AssetConstants.indicatorGreen,
                onPressed: context.read<CameraCubit>().switchCamera,
              ),
              ButtonGroup(
                player: player,
                icon: AssetConstants.focusLarge,
                indicator: AssetConstants.indicatorOff,
                onPressed: () async {
                  // Add your button press logic here
                },
              ),
              ButtonGroup(
                player: player,
                icon: AssetConstants.nightMode,
                indicator: AssetConstants.indicatorOff,
                onPressed: () async {
                  // Add your button press logic here
                },
              ),
            ],
          ),
        );
      },
    );
  }
}

class ButtonGroup extends StatelessWidget {
  final AudioPlayer player;
  final String icon;
  final String indicator;
  final double baseSize;
  final Future<void> Function() onPressed;

  const ButtonGroup({
    super.key,
    required this.player,
    required this.icon,
    required this.indicator,
    required this.onPressed,
    this.baseSize = 50,
  });

  @override
  Widget build(BuildContext context) {
    final metrics = ButtonMetrics(baseSize);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        ScalableButton(
          player: player,
          icon: icon,
          size: baseSize,
          onPressed: onPressed,
        ),
        SizedBox(height: metrics.spacing),
        Image.asset(
          indicator,
          width: metrics.indicatorSize.width,
          height: metrics.indicatorSize.height,
          fit: BoxFit.contain,
        ),
      ],
    );
  }
}

class ScalableButton extends StatefulWidget {
  final AudioPlayer player;
  final String icon;
  final double size;
  final Future<void> Function() onPressed;

  // Scaling factors calculated dynamically based on the size
  late final ButtonMetrics _metrics;

  ScalableButton({
    super.key,
    required this.player,
    required this.icon,
    required this.size,
    required this.onPressed,
  }) : _metrics = ButtonMetrics(size);

  @override
  State<ScalableButton> createState() => _ScalableButtonState();
}

class _ScalableButtonState extends State<ScalableButton>
    with SingleTickerProviderStateMixin {
  final ValueNotifier<double> _scale = ValueNotifier(1.0);
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    _scale.dispose();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) async {
    _scale.value = 0.95; // Scale down on press
    widget.player.stop();
    widget.player.seek(Duration.zero);
    await widget.player.setPlaybackRate(3);
    widget.player.play(AssetSource(AssetConstants.buttonPressBeep));
  }

  void _onTapUp(TapUpDetails details) {
    _scale.value = 1.0; // Scale back up on release
  }

  void _onTapCancel() {
    _scale.value = 1.0;
  }

  Future<void> _selectOption(Future<void> Function() callback) async {
    await callback();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.size,
      height: widget.size,
      child: Stack(
        alignment: Alignment.center,
        children: [
          _buildButtonLayer(
            AssetConstants.silverButtonRingShadow,
            widget._metrics.outerSize,
          ),
          _buildButtonLayer(
            AssetConstants.silverButtonRingDark,
            widget._metrics.ringSize,
          ),
          _buildButtonLayer(
            AssetConstants.silverButtonShadow,
            widget._metrics.shadowSize,
          ),
          GestureDetector(
            onTapDown: _onTapDown,
            onTapUp: _onTapUp,
            onTapCancel: _onTapCancel,
            onTap: () async => await _selectOption(widget.onPressed),
            child: ValueListenableBuilder(
              valueListenable: _scale,
              builder: (context, scale, child) => AnimatedScale(
                scale: scale,
                duration: const Duration(milliseconds: 50),
                child: Stack(
                  children: [
                    _buildButtonLayer(
                      AssetConstants.silverButtonDark,
                      widget._metrics.buttonSize,
                    ),
                    if (_scale.value < 1.0)
                      Container(
                        width: widget._metrics.buttonSize.width,
                        height: widget._metrics.buttonSize.height,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: Colors.black.withValues(alpha: 0.2),
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ),
          IgnorePointer(
            child: RotatedBox(
              quarterTurns: 1,
              child: Image.asset(
                widget.icon,
                width: widget._metrics.iconSize.width,
                height: widget._metrics.iconSize.height,
                fit: BoxFit.contain,
                color: Colors.grey.withValues(alpha: .7),
                colorBlendMode: BlendMode.srcIn,
              ),
            ),
          )
        ],
      ),
    );
  }

  Widget _buildButtonLayer(String asset, Size dimensions) {
    return Image.asset(
      asset,
      width: dimensions.width,
      height: dimensions.height,
      fit: BoxFit.contain,
    );
  }
}

class ButtonMetrics {
  final double baseSize;

  late final Size outerSize;
  late final Size ringSize;
  late final Size shadowSize;
  late final Size buttonSize;
  late final Size indicatorSize;
  late final Size iconSize;
  late final double spacing;

  ButtonMetrics(this.baseSize) {
    _calculateSizes();
  }

  void _calculateSizes() {
    outerSize = Size(baseSize, baseSize);
    ringSize = Size.square(baseSize * 0.94); // 80/85
    shadowSize = Size.square(baseSize * 0.69); // 59/85
    buttonSize = Size.square(baseSize * 0.65); // 55/85

    // Icon size relative to button size (adjust scale factor as needed)
    iconSize = Size.square(baseSize * 0.32); // Makes icon 32% of base size

    indicatorSize = Size.square(baseSize * 0.30);
    spacing = baseSize * 0.1;
  }

  ButtonMetrics scaled(double factor) {
    return ButtonMetrics(baseSize * factor);
  }
}
