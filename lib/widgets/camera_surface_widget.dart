import 'package:flutter/material.dart';

import 'package:fujimo/constants/asset_constants.dart';

class CameraSurfaceWidget extends StatelessWidget {
  final Widget child;
  const CameraSurfaceWidget({super.key, required this.child});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Image.asset(
          AssetConstants.cameraScreenSurface,
          width: double.infinity,
          height: double.infinity,
          fit: BoxFit.fill,
        ),
        Positioned.fill(
          child: child,
        ),
      ],
    );
  }
}
