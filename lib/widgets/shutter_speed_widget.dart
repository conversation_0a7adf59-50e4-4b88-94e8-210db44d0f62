import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:fujimo/global/capsule_ruler_widget.dart';

class ShutterSpeedWidget extends StatefulWidget {
  const ShutterSpeedWidget({super.key});

  @override
  State<ShutterSpeedWidget> createState() => _ShutterSpeedWidgetState();
}

class _ShutterSpeedWidgetState extends State<ShutterSpeedWidget> {
  double _value = 0.0;
  double _calculatedValue = 0.0;


  final double _doubleRatio = 50.0;
  final double minValue = 0.0;
  final double maxValue = 40.0;

  @override
  Widget build(BuildContext context) {
    return RotatedBox(
      quarterTurns: 1,
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 10.0, horizontal: 60),
        child: CapsuleRuler(
          minValue: minValue,
          maxValue: maxValue,

          value: _value,
          height: 30,
          backgroundColor: Colors.black,
          tickColor: Colors.white,
          tickBorderRadius: 3.0,
          majorTickWidth: 2.0,
          minorTickHeightRatio: 0.5,

          mediumTickHeightRatio: 0.5,

          majorTickHeightRatio: 0.8,

          mediumTickWidth: 2.0,
          minorTickWidth: 2.0,

          capsuleColor: Colors.amber,
          capsuleBorderColor: Colors.white,
          capsuleBorderWidth: 0.8,
          capsuleBackgroundColor: Colors.amber,
          capsuleShadowColor: Colors.white.withAlpha(100),
          capsuleShadowElevation: 2.0,
          capsuleWidth: 5, // Narrower width for the full-height capsule
          cornerRadius: 4,
          //rulerPadding: 5,
          minorTicksPerMajor: 0,
          majorTickInterval: 10,
          enableSnapping: true,
          forceFullWidth: false,
          showLabels: false,
          snapInterval: 1,

          autoAdaptToSpace: false,

          onChanged: (value) {
            setState(() {
              _value = value.clamp(minValue, maxValue);
              _calculatedValue = _value * _doubleRatio;
              log('shutter speed: $_calculatedValue');
            });
          },

          startWidget: Container(
            margin: const EdgeInsets.symmetric(vertical: 6),
            padding: const EdgeInsets.symmetric(horizontal: 3, vertical: 2),
            decoration: BoxDecoration(
                gradient: const LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    //* active
                    // Color.fromARGB(255, 255, 201, 126),
                    // Color.fromARGB(255, 207, 137, 39)

                    //* disable
                    Color.fromARGB(255, 218, 218, 218),
                    Color.fromARGB(255, 122, 122, 122)
                  ],
                ),
                border: Border.all(
                  color: const Color.fromARGB(255, 37, 37, 37),
                  width: 0.8,
                ),
                borderRadius: BorderRadius.circular(2)),
            child: FittedBox(
              child: Center(
                child: Stack(
                  children: [
                    // Stroke
                    Text(
                      'AUTO',
                      style: TextStyle(
                        fontFamily: 'Oxanium',
                        fontSize: 40,
                        foreground: Paint()
                          ..style = PaintingStyle.stroke
                          ..strokeWidth = 8
                          ..color = Colors.black,
                      ),
                    ),
                    // Fill
                    const Text(
                      'AUTO',
                      style: TextStyle(
                        fontFamily: 'Oxanium',
                        fontSize: 40,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
