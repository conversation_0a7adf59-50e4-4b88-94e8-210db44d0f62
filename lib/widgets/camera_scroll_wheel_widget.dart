import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:fujimo/controllers/camera-cubit/camera_cubit.dart';
import 'package:fujimo/constants/asset_constants.dart';

class CameraScrollWheelWidget extends StatefulWidget {
  const CameraScrollWheelWidget({super.key});

  @override
  State<CameraScrollWheelWidget> createState() =>
      _CameraScrollWheelWidgetState();
}

class _CameraScrollWheelWidgetState extends State<CameraScrollWheelWidget> {
  late final ScrollController _controller;

  @override
  void initState() {
    super.initState();
    _controller = ScrollController();
    _controller.addListener(scrollListener);

  }

  @override
  void dispose() {
    _controller.removeListener(scrollListener);
    _controller.dispose();
    super.dispose();
  }

  void scrollListener() {
    if(!_controller.hasClients) return;
    final double progress = _controller.offset / _controller.position.maxScrollExtent;
    final int zoomLevel = (progress * 100).round().clamp(0, 100);
    context.read<CameraCubit>().setZoom(zoomLevel.toDouble());
  }

  @override
  Widget build(BuildContext context) {
    return RotatedBox(
      quarterTurns: 1,
      child: Stack(
        alignment: Alignment.center,
        children: [
          Center(
            child: SizedBox(
              width: 160,
              height: 50,
              child: SingleChildScrollView(
                controller: _controller,
                scrollDirection: Axis.horizontal,
                child: SizedBox(
                  height: 30,
                  width: 320,
                  child: Image.asset(
                    AssetConstants.layer5,
                    fit: BoxFit.fill,
                  ),
                ),
              ),
            ),
          ),
          IgnorePointer(
            child: Center(
              child: Image.asset(
                AssetConstants.layer7,
                height: 39,
                fit: BoxFit.cover,
                width: 165,
                color: Colors.black,
              ),
            ),
          ),
          IgnorePointer(
            child: Center(
              child: Image.asset(
                AssetConstants.layer4,
                height: 40,
                fit: BoxFit.cover,
                width: 165,
              ),
            ),
          ),
          IgnorePointer(
            child: Center(
              child: Image.asset(
                AssetConstants.layer3,
                height: 50,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

