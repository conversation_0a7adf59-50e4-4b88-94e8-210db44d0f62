import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart' show BlocBuilder;
import 'package:fujimo/constants/asset_constants.dart';
import 'package:fujimo/controllers/battery-cubit/battery_cubit.dart';
import 'dart:math' as math;

import 'package:fujimo/controllers/camera-cubit/camera_cubit.dart';
import 'package:fujimo/services/camera_service.dart';


/// A camera display widget that shows camera information in a retro digital style.
/// This widget maintains its aspect ratio and scales all elements proportionally.
class CameraDisplayWidget extends StatelessWidget {
  /// The width of the widget.
  final double width;

  /// The height of the widget. If null, it will be calculated based on the width
  /// to maintain the aspect ratio of 2.92:1 (width:height).
  final double? height;

  /// The color of active elements in the display.
  final Color activeColor;

  /// The ISO value to display.
  final String iso;

  /// The aperture value to display.
  final String aperture;

  /// Background color of the display.
  final Color backgroundColor;

  /// Gradient colors for the background. If provided, overrides backgroundColor.
  final List<Color>? gradientColors;

  /// Gradient begin alignment.
  final Alignment gradientBegin;

  /// Gradient end alignment.
  final Alignment gradientEnd;

  /// Border radius of the display.
  final double borderRadius;

  /// Whether to show a border around the display.
  final bool showBorder;

  /// Border color of the display.
  final Color borderColor;

  /// Border width of the display.
  final double borderWidth;

  /// Whether to show a shadow around the display.
  final bool showShadow;

  /// Shadow color of the display.
  final Color shadowColor;

  /// Shadow blur radius.
  final double shadowBlurRadius;

  /// Shadow offset.
  final Offset shadowOffset;

  /// Text color for labels (ISO, F, T).
  final Color labelTextColor;

  /// Text color for values (100, 5.7, 4).
  final Color valueTextColor;

  /// Icon color for all icons.
  final Color iconColor;

  /// Bracket color for the bracket widget.
  final Color bracketColor;

  /// Font family for digital display values.
  final String digitalFontFamily;

  /// Padding around the display content.
  final EdgeInsets padding;

  /// Space between the left and right sections.
  final double sectionSpacing;

  /// Path to the battery icon asset.
  final String batteryIconAsset;

  /// Path to the flash auto icon asset.
  final String flashAutoIconAsset;

  /// Path to the flash off icon asset.
  final String flashOffIconAsset;

  /// Path to the flash on icon asset.
  final String flashOnIconAsset;

  /// Path to the light icon asset.
  final String lightIconAsset;

  /// Whether to show the retro glow effect under digital values.
  final bool showGlowEffect;

  /// Color of the glow effect.
  final Color glowColor;

  /// Whether to use fixed-width for digital values.
  final bool useFixedWidth;

  /// Whether to pad with zeros instead of spaces.
  final bool padWithZeros;

  /// Whether to show inactive digit places (8s in the background).
  final bool showInactiveDigits;

  /// Color for inactive digit places.
  final Color inactiveDigitColor;

  const CameraDisplayWidget({
    super.key,
    required this.width,
    this.height,
    this.activeColor = const Color(0xFFFF8A00),
    this.iso = '100',
    this.aperture = '5.7',
    this.backgroundColor = const Color(0xFF1A1A1A),
    this.gradientColors = const [Color(0xFF222222), Color(0xFF111111)],
    this.gradientBegin = const Alignment(0.8, 0.8),
    this.gradientEnd = const Alignment(-0.8, -0.8),
    this.borderRadius = 8.0,
    this.showBorder = true,
    this.borderColor = const Color(0xFFFF8A00),
    this.borderWidth = 2.0,
    this.showShadow = true,
    this.shadowColor = Colors.black,
    this.shadowBlurRadius = 15.0,
    this.shadowOffset = const Offset(5, 5),
    this.labelTextColor = Colors.white,
    this.valueTextColor = Colors.white,
    this.iconColor = Colors.white,
    this.bracketColor = Colors.white,
    this.digitalFontFamily = 'DSDigitalItalic',
    this.padding = const EdgeInsets.symmetric(horizontal: 8.0, vertical: 8.0),
    this.sectionSpacing = 20.0,
    this.batteryIconAsset = 'assets/camera-components/battery1.png',
    this.flashAutoIconAsset = 'assets/camera-components/flash-auto.png',
    this.flashOffIconAsset = 'assets/camera-components/flash-off.png',
    this.flashOnIconAsset = 'assets/camera-components/flash-on.png',
    this.lightIconAsset = 'assets/camera-components/light.png',
    this.showGlowEffect = true,
    this.glowColor = const Color(0x80FF8A00), // Semi-transparent orange
    this.useFixedWidth = true,
    this.padWithZeros = false, // Default to spaces, not zeros
    this.showInactiveDigits = true,
    this.inactiveDigitColor = const Color(0x40FF8A00), // Very dim orange
  });

  @override
  Widget build(BuildContext context) {
    // Calculate height based on width if not provided, maintaining aspect ratio
    final containerWidth = width;
    final containerHeight = height ?? containerWidth / 3.8;

    // Calculate scale factor based on the reference width of 350
    final scaleFactor = containerWidth / 350.0;

    // Scale the padding based on the scale factor
    final scaledPadding = EdgeInsets.fromLTRB(
      padding.left * scaleFactor,
      padding.top * scaleFactor,
      padding.right * scaleFactor,
      padding.bottom * scaleFactor,
    );

    String getBatteryLevelAsset(int level) {
      if (level >= 80) {
        return AssetConstants.batteryLevel1;
      } else if (level >= 60) {
        return AssetConstants.batteryLevel2;
      } else if (level >= 40) {
        return AssetConstants.batteryLevel3;
      } else if (level >= 20) {
        return AssetConstants.batteryLevel4;
      } else {
        return AssetConstants.batteryLevel5;
      }
    }

    return Container(
      width: containerWidth,
      height: containerHeight,
      padding: scaledPadding,
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(borderRadius * scaleFactor),
        border: showBorder
            ? Border.all(
                color: borderColor.withAlpha(100),
                width: borderWidth * scaleFactor,
              )
            : null,
        gradient: gradientColors != null
            ? LinearGradient(
                begin: gradientBegin,
                end: gradientEnd,
                colors: gradientColors!,
              )
            : null,
        boxShadow: showShadow
            ? [
                BoxShadow(
                  color: shadowColor.withAlpha(150),
                  blurRadius: shadowBlurRadius * scaleFactor,
                  offset: Offset(
                    shadowOffset.dx * scaleFactor,
                    shadowOffset.dy * scaleFactor,
                  ),
                ),
              ]
            : null,
      ),
      child: FittedBox(
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Column(
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  spacing: 16 * scaleFactor,
                  children: [
                    // Battery icon
                    BlocBuilder<BatteryCubit, BatteryState>(
                      builder: (context, state) {
                        final batteryLevelImage =
                            getBatteryLevelAsset(state.level);
                        return Container(
                          margin: EdgeInsets.only(top: 10 * scaleFactor),
                          child: Image.asset(
                            batteryLevelImage,
                            width: 50 * scaleFactor * 1.5,
                            height: 35 * scaleFactor * 1.5,
                            color: iconColor,
                          ),
                        );
                      },
                    ),

                    Padding(
                      padding: EdgeInsets.only(top: 10 * scaleFactor),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        spacing: 10 * scaleFactor,
                        children: [
                          Row(
                            spacing: 0,
                            children: [
                              BlocBuilder<CameraCubit, CameraState>(
                                builder: (context, state) {
                                  final configs = state as CameraConfiguration;
                                  return BracketWidget(
                                    iconAsset: flashAutoIconAsset,
                                    iconSize: 28 * scaleFactor * 1.5,
                                    width: 50 * scaleFactor * 1.5,
                                    height: 50 * scaleFactor * 1.5,
                                    selected: configs.flashMode ==
                                        CameraFlashMode.auto,
                                    iconColor: iconColor,
                                    bracketColor: bracketColor,
                                  );
                                },
                              ),
                              BlocBuilder<CameraCubit, CameraState>(
                                builder: (context, state) {
                                  final configs = state as CameraConfiguration;
                                  return BracketWidget(
                                    iconAsset: flashOffIconAsset,
                                    iconSize: 28 * scaleFactor * 1.5,
                                    width: 50 * scaleFactor * 1.5,
                                    height: 50 * scaleFactor * 1.5,
                                    selected: configs.flashMode ==
                                        CameraFlashMode.off,
                                    iconColor: iconColor,
                                    bracketColor: bracketColor,
                                  );
                                },
                              ),
                              BlocBuilder<CameraCubit, CameraState>(
                                builder: (context, state) {
                                  final configs = state as CameraConfiguration;
                                  return BracketWidget(
                                    iconAsset: flashOnIconAsset,
                                    iconSize: 28 * scaleFactor * 1.5,
                                    width: 50 * scaleFactor * 1.5,
                                    height: 50 * scaleFactor * 1.5,
                                    selected: configs.flashMode ==
                                        CameraFlashMode.always,
                                    iconColor: iconColor,
                                    bracketColor: bracketColor,
                                  );
                                },
                              ),
                              BlocBuilder<CameraCubit, CameraState>(
                                builder: (context, state) {
                                  final configs = state as CameraConfiguration;
                                  return BracketWidget(
                                    iconAsset: lightIconAsset,
                                    iconSize: 28 * scaleFactor * 1.5,
                                    width: 50 * scaleFactor * 1.5,
                                    height: 50 * scaleFactor * 1.5,
                                    selected: configs.flashMode ==
                                        CameraFlashMode.torch,
                                    iconColor: iconColor,
                                    bracketColor: bracketColor,
                                  );
                                },
                              ),
                            ],
                          ),
                          Padding(
                            padding: EdgeInsets.only(left: 25 * scaleFactor),
                            child: BlocBuilder<CameraCubit, CameraState>(
                              builder: (context, state) {
                                final timerValue = state is CameraConfiguration
                                    ? state.selectedTimer.toString()
                                    : "0";
                                return IndicatorText(
                                  title: 'T',
                                  value: timerValue,
                                  titleFontSize: 35 * scaleFactor,
                                  valueFontSize: 55 * scaleFactor * 1.2,
                                  titleColor: labelTextColor,
                                  valueColor: valueTextColor,
                                  valueFontFamily: digitalFontFamily,
                                  valueWidth: useFixedWidth
                                      ? 2
                                      : 0, // Fixed width of 2 characters
                                  padWithZeros: padWithZeros,
                                  showGlowEffect: showGlowEffect,
                                  glowColor: glowColor,
                                  showInactiveDigits: showInactiveDigits,
                                  inactiveDigitColor: inactiveDigitColor,
                                );
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
            SizedBox(width: sectionSpacing * scaleFactor),
            Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // ISO display
                IndicatorText(
                  title: 'ISO',
                  value: iso,
                  titleFontSize: 35 * scaleFactor,
                  valueFontSize: 70 * scaleFactor,
                  titleColor: labelTextColor,
                  valueColor: valueTextColor,
                  valueFontFamily: digitalFontFamily,
                  valueWidth:
                      useFixedWidth ? 4 : 0, // Fixed width of 4 characters
                  padWithZeros: false, // Always use spaces for ISO, not zeros
                  showGlowEffect: showGlowEffect,
                  glowColor: glowColor,
                  showInactiveDigits: showInactiveDigits,
                  inactiveDigitColor: inactiveDigitColor,
                ),

                // Aperture display
                IndicatorText(
                  title: 'F',
                  value: aperture,
                  titleFontSize: 35 * scaleFactor,
                  valueFontSize: 60 * scaleFactor,
                  titleColor: labelTextColor,
                  valueColor: valueTextColor,
                  valueFontFamily: digitalFontFamily,
                  valueWidth: useFixedWidth
                      ? 4
                      : 0, // Fixed width of 4 characters to accommodate values like "11.0"
                  padWithZeros: false, // Don't pad with zeros for aperture
                  showGlowEffect: showGlowEffect,
                  glowColor: glowColor,
                  showInactiveDigits: showInactiveDigits,
                  inactiveDigitColor: inactiveDigitColor,
                  hasDecimalPoint: true, // Aperture always has a decimal point
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class IndicatorText extends StatelessWidget {
  final String title;
  final String value;
  final double titleFontSize;
  final double valueFontSize;
  final Color titleColor;
  final Color valueColor;
  final String valueFontFamily;
  final int valueWidth; // Number of characters to display (for fixed width)
  final bool padWithZeros; // Whether to pad with zeros or spaces
  final bool showGlowEffect; // Whether to show the retro glow effect
  final Color glowColor; // Color of the glow effect
  final bool showInactiveDigits; // Whether to show inactive digit places
  final Color inactiveDigitColor; // Color for inactive digit places
  final bool
      hasDecimalPoint; // Whether the value has a decimal point (for aperture)

  const IndicatorText(
      {super.key,
      required this.title,
      required this.value,
      this.titleFontSize = 14,
      this.valueFontSize = 55,
      this.titleColor = Colors.white,
      this.valueColor = Colors.white,
      this.valueFontFamily = 'DSDigitalItalic',
      this.valueWidth = 0, // 0 means auto width
      this.padWithZeros = false,
      this.showGlowEffect = true,
      this.glowColor = const Color(0x80FF8A00), // Semi-transparent orange
      this.showInactiveDigits = true,
      this.inactiveDigitColor = const Color(0x40FF8A00), // Very dim orange
      this.hasDecimalPoint = false});

  @override
  Widget build(BuildContext context) {
    // Format the value to have fixed width if specified
    String displayValue = value;

    // Special handling for decimal values (like aperture)
    bool hasDecimal = hasDecimalPoint || displayValue.contains('.');

    if (valueWidth > 0) {
      if (hasDecimal) {
        // For values with decimal points, we need to ensure proper alignment
        // We'll pad the whole part and keep the decimal part as is
        List<String> parts = displayValue.split('.');
        String wholePart = parts[0];
        String decimalPart = parts.length > 1 ? parts[1] : '';

        // Calculate how many digits we need for the whole part
        // For aperture, we typically want 1 digit for whole part, 1 for decimal point, and 1 for decimal part
        int wholePartWidth =
            valueWidth - decimalPart.length - 1; // -1 for the decimal point

        if (wholePart.length < wholePartWidth) {
          String padding = padWithZeros ? '0' : ' ';
          wholePart = wholePart.padLeft(wholePartWidth, padding);
        } else if (wholePart.length > wholePartWidth) {
          // Truncate if too long
          wholePart = wholePart.substring(0, wholePartWidth);
        }

        displayValue = '$wholePart.$decimalPart';
      } else {
        // Standard padding for non-decimal values
        if (displayValue.length < valueWidth) {
          String padding = padWithZeros ? '0' : ' ';
          displayValue = displayValue.padLeft(valueWidth, padding);
        } else if (displayValue.length > valueWidth) {
          // Truncate if too long
          displayValue = displayValue.substring(0, valueWidth);
        }
      }
    }

    // Split the display value into individual characters
    List<String> characters = displayValue.split('');

    // For fixed width, ensure we have the correct number of characters
    // This is important for ISO values where we want to show 4 digits
    if (valueWidth > 0 && characters.length < valueWidth) {
      // Add spaces at the beginning to reach the desired width
      int spacesToAdd = valueWidth - characters.length;
      for (int i = 0; i < spacesToAdd; i++) {
        characters.insert(0, ' ');
      }
    }

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Title (ISO, F, T)
        Text(
          title,
          style: TextStyle(
            fontSize: titleFontSize,
            fontFamily: 'Oxanium',
            height: 0,
            color: titleColor,
          ),
        ),
        const SizedBox(width: 5),

        // Container for the digits with fixed width
        Row(
          mainAxisSize: MainAxisSize.min,
          children: List.generate(characters.length, (index) {
            final char = characters[index];

            // Check if this character is a decimal point
            final isDecimalPoint = char == '.';

            // Check if this character is a space (for padding)
            final isSpace = char == ' ';

            // Use a SizedBox with fixed width to ensure consistent spacing
            return SizedBox(
              // Use a width that matches the width of the character
              // Decimal points need less space than digits
              width: isDecimalPoint
                  ? valueFontSize * 0.3
                  : valueFontSize *
                      0.6, // Adjust these values based on your font
              child: Stack(
                alignment: Alignment
                    .centerRight, // Right-align digits for proper display
                children: [
                  // Background "8" for each digit (including spaces for fixed width)
                  // This ensures we show the correct number of "8"s for fixed width
                  if (showInactiveDigits && !isDecimalPoint)
                    Align(
                      alignment: Alignment.centerRight,
                      child: Text(
                        '8', // Always show "8" for background, regardless of the actual character
                        style: TextStyle(
                          fontSize: valueFontSize,
                          fontFamily: valueFontFamily,
                          height: 0,
                          color: inactiveDigitColor,
                        ),
                      ),
                    ),

                  // Background "." for decimal point
                  if (showInactiveDigits && isDecimalPoint)
                    Align(
                      alignment: Alignment.centerRight,
                      child: Text(
                        '.',
                        style: TextStyle(
                          fontSize: valueFontSize,
                          fontFamily: valueFontFamily,
                          height: 0,
                          color: inactiveDigitColor,
                        ),
                      ),
                    ),

                  // Glow effect for each digit
                  if (showGlowEffect && !isSpace)
                    Positioned(
                      right: 0,
                      top: 2, // Slight offset for shadow effect
                      child: Text(
                        char,
                        style: TextStyle(
                          fontSize: valueFontSize,
                          fontFamily: valueFontFamily,
                          height: 0,
                          color: glowColor,
                          shadows: [
                            Shadow(
                              color: glowColor,
                              blurRadius: 8,
                            ),
                          ],
                        ),
                      ),
                    ),

                  // Main text for each digit
                  Align(
                    alignment: Alignment.centerRight,
                    child: Text(
                      char,
                      style: TextStyle(
                        fontSize: valueFontSize,
                        fontFamily: valueFontFamily,
                        height: 0,
                        color: valueColor,
                      ),
                    ),
                  ),
                ],
              ),
            );
          }),
        ),
      ],
    );
  }
}

class BracketWidget extends StatelessWidget {
  final String? iconAsset; // Path to the icon asset
  final IconData? icon; // Optional fallback to IconData
  final double width;
  final double height;
  final double cornerRadius;
  final double bracketExtension;
  final double bracketGap;
  final Color bracketColor;
  final double strokeWidth;
  final EdgeInsets padding;
  final Color iconColor;
  final double iconSize;
  final bool selected; // Whether to show the bracket around the icon

  const BracketWidget({
    super.key,
    this.iconAsset,
    this.icon,
    this.width = 50,
    this.height = 50,
    this.cornerRadius = 4.0,
    this.bracketExtension = 0.3,
    this.bracketGap = 0.2,
    this.bracketColor = Colors.white,
    this.strokeWidth = 2.0,
    this.padding = const EdgeInsets.all(4.0),
    this.iconColor = Colors.white,
    this.iconSize = 24,
    this.selected = true, // Default to showing the bracket
  }) : assert(iconAsset != null || icon != null,
            'Either iconAsset or icon must be provided');

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: width,
      height: height,
      child: Stack(
        children: [
          // Only show the bracket if showBracket is true
          if (selected)
            CustomPaint(
              size: Size(width, height),
              painter: BracketPainter(
                cornerRadius: cornerRadius,
                bracketExtension: bracketExtension,
                bracketGap: bracketGap,
                color: bracketColor,
                strokeWidth: strokeWidth,
                padding: padding,
              ),
            ),
          Center(
            child: iconAsset != null
                ? Image.asset(
                    iconAsset!,
                    width: iconSize,
                    height: iconSize,
                    color: selected ? iconColor : iconColor.withAlpha(40),
                  )
                : Icon(
                    icon,
                    color: selected ? iconColor : iconColor.withAlpha(40),
                    size: iconSize,
                  ),
          ),
        ],
      ),
    );
  }
}

class BracketPainter extends CustomPainter {
  final double cornerRadius; // Controls corner roundness
  final double bracketExtension; // How far brackets extend (0.0 to 1.0)
  final double bracketGap; // Space between brackets (0.0 to 1.0)
  final Color color; // Bracket color
  final double strokeWidth; // Line thickness
  final EdgeInsets padding; // Internal padding

  BracketPainter({
    this.cornerRadius = 8.0,
    this.bracketExtension = 0.3,
    this.bracketGap = 0.2, // This now represents the total gap size
    this.color = Colors.white,
    this.strokeWidth = 2.0,
    this.padding = EdgeInsets.zero,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke;

    // Calculate padded dimensions
    final paddedWidth = size.width - (padding.left + padding.right);
    final paddedHeight = size.height - (padding.top + padding.bottom);
    final startX = padding.left;
    final startY = padding.top;

    // Ensure cornerRadius doesn't exceed the minimum dimension
    final effectiveRadius = math.min(
      cornerRadius,
      math.min(paddedWidth / 2, paddedHeight / 2),
    );

    // Calculate the center point
    final centerY = startY + (paddedHeight / 2);

    // Calculate the gap (half above and half below center)
    final halfGap = (paddedHeight * bracketGap) / 2;

    // Calculate bracket positions from the center
    final topBracketBottom = centerY - halfGap;
    final bottomBracketTop = centerY + halfGap;

    // Top bracket
    final topPath = Path()
      ..moveTo(startX, topBracketBottom)
      ..lineTo(startX, startY + effectiveRadius)
      ..arcToPoint(
        Offset(startX + effectiveRadius, startY),
        radius: Radius.circular(effectiveRadius),
        clockwise: true,
      )
      ..lineTo(startX + paddedWidth - effectiveRadius, startY)
      ..arcToPoint(
        Offset(startX + paddedWidth, startY + effectiveRadius),
        radius: Radius.circular(effectiveRadius),
        clockwise: true,
      )
      ..lineTo(startX + paddedWidth, topBracketBottom);

    // Bottom bracket
    final bottomPath = Path()
      ..moveTo(startX, bottomBracketTop)
      ..lineTo(startX, startY + paddedHeight - effectiveRadius)
      ..arcToPoint(
        Offset(startX + effectiveRadius, startY + paddedHeight),
        radius: Radius.circular(effectiveRadius),
        clockwise: false,
      )
      ..lineTo(startX + paddedWidth - effectiveRadius, startY + paddedHeight)
      ..arcToPoint(
        Offset(startX + paddedWidth, startY + paddedHeight - effectiveRadius),
        radius: Radius.circular(effectiveRadius),
        clockwise: false,
      )
      ..lineTo(startX + paddedWidth, bottomBracketTop);

    canvas.drawPath(topPath, paint);
    canvas.drawPath(bottomPath, paint);
  }

  @override
  bool shouldRepaint(covariant BracketPainter oldDelegate) {
    return oldDelegate.cornerRadius != cornerRadius ||
        oldDelegate.bracketExtension != bracketExtension ||
        oldDelegate.bracketGap != bracketGap ||
        oldDelegate.color != color ||
        oldDelegate.strokeWidth != strokeWidth ||
        oldDelegate.padding != padding;
  }
}

/// Extension to add spacing to Row and Column
extension SpacingExtension on Widget {
  Widget spacing(double value) {
    if (this is Row) {
      return Row(
        mainAxisSize: (this as Row).mainAxisSize,
        mainAxisAlignment: (this as Row).mainAxisAlignment,
        crossAxisAlignment: (this as Row).crossAxisAlignment,
        textDirection: (this as Row).textDirection,
        verticalDirection: (this as Row).verticalDirection,
        textBaseline: (this as Row).textBaseline,
        children: _addSpacing((this as Row).children, value, true),
      );
    } else if (this is Column) {
      return Column(
        mainAxisSize: (this as Column).mainAxisSize,
        mainAxisAlignment: (this as Column).mainAxisAlignment,
        crossAxisAlignment: (this as Column).crossAxisAlignment,
        textDirection: (this as Column).textDirection,
        verticalDirection: (this as Column).verticalDirection,
        textBaseline: (this as Column).textBaseline,
        children: _addSpacing((this as Column).children, value, false),
      );
    }
    return this;
  }

  List<Widget> _addSpacing(
      List<Widget> children, double value, bool horizontal) {
    if (children.isEmpty) return children;

    final spacedChildren = <Widget>[];
    for (var i = 0; i < children.length; i++) {
      spacedChildren.add(children[i]);
      if (i < children.length - 1) {
        spacedChildren.add(
          horizontal ? SizedBox(width: value) : SizedBox(height: value),
        );
      }
    }
    return spacedChildren;
  }
}
