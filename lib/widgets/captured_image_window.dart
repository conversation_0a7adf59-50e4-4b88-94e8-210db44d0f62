import 'package:flutter/material.dart';
import 'package:fujimo/constants/asset_constants.dart';

/// A widget that displays a captured image in a retro-style window frame.
/// Handles image display, shadow effects, and window overlay.
class CapturedImageWindow extends StatelessWidget {
  // Constants for dimensions
  static const double _windowWidth = 100.0;
  static const double _windowHeight = 70.0;
  static const double _imageScale = 0.9;
  static const double _shadowOffset = 14.0;
  static const double _padding = 10.0;

  static final Widget _shadowWidget = Image.asset(
    AssetConstants.imagePlaceShadow,
    width: _windowWidth,
    height: _windowHeight,
  );

  static final Widget _windowFrame = Image.asset(
    AssetConstants.imageWindow,
    width: _windowWidth,
    height: _windowHeight,
    // Caching the image in memory
    // cacheWidth: (_windowWidth * 2).toInt(),
    // cacheHeight: (_windowHeight * 2).toInt(),
  );

  final Image? displayImage;

  const CapturedImageWindow({
    super.key, 
    required this.displayImage,
  });

  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(
      child: RotatedBox(
        quarterTurns: 3,
        child: Padding(
          padding: const EdgeInsets.all(_padding),
          child: SizedBox(
            width: _windowWidth + _shadowOffset,
            height: _windowHeight + _shadowOffset,
            child: Stack(
              alignment: Alignment.center,
              children: [
                // Shadow layer
                Center(
                  child: SizedBox(
                    width: _windowWidth + _shadowOffset,
                    height: _windowHeight + _shadowOffset,
                    child: FittedBox(
                      //fit: BoxFit.cover,
                      child: _shadowWidget,
                    ),
                  ),
                ),
                // Image layer
                Center(
                  child: SizedBox(
                    width: _windowWidth * _imageScale,
                    height: _windowHeight * _imageScale,
                    child: _buildImageWidget(),
                  ),
                ),
                // Window frame overlay
                Center(child: _windowFrame),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildImageWidget() {
    if (displayImage == null) {
      return Container(color: Colors.transparent);
    }

    return RotatedBox(
      quarterTurns: 1,
      child: ClipRRect(
        child: SizedBox.expand(
          child: FittedBox(
            fit: BoxFit.cover,
            child: displayImage!,
          ),
        ),
      ),
    );
  }
}
