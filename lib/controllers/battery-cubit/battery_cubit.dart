import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart' show Cubit;
import 'package:fujimo/services/battery_service.dart';
import 'package:get_it/get_it.dart';

part 'battery_state.dart';

class BatteryCubit extends Cubit<BatteryState> {
  final BatteryService _batteryService = GetIt.instance<BatteryService>();
  StreamSubscription? _levelSubscription;

  BatteryCubit() : super(BatteryState(level: 100));


  Future<void> initializeBattery() async {
    await _batteryService.initBattery();
    emit(BatteryState(level: 0));

    _levelSubscription = _batteryService.batteryLevelStream.listen((level) {
      emit(state.copyWith(level: level));
    });
  }

  @override
  Future<void> close() {
    _levelSubscription?.cancel();
    return super.close();
  }
}
