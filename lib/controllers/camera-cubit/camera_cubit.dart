import 'dart:async';

import 'package:camera/camera.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:fujimo/services/camera_service.dart';
import 'package:get_it/get_it.dart';

part 'camera_state.dart';

class CameraCubit extends Cubit<CameraState> {
  final CameraService _cameraService = GetIt.instance<CameraService>();
  // Timer? _timer;
  // Timer? _blinkTimer;

  CameraCubit()
      : super(
          CameraConfiguration(
            selectedCamera: SelectedCamera.back,
            isTorchOn: false,
            flashMode: CameraFlashMode.off,
            isInitialized: false,
            cameraController: null,
          ),
        );

  Future<void> initializeCamera() async {
    try {
      await _cameraService.initializeCamera();
      emit(
        CameraConfiguration(
            selectedCamera: _cameraService.selectedCamera,
            isTorchOn: _cameraService.flashOnOff,
            flashMode: _cameraService.flashMode,
            isInitialized: _cameraService.isInitialized,
            cameraController: _cameraService.cameraController),
      );
    } catch (e) {
      emit(CameraError('Failed to initialize the camera: $e'));
    }
  }

  Future<void> switchCamera() async {
    try {
      await _cameraService.switchCamera();

      final currentConfig = state as CameraConfiguration;
      emit(currentConfig.copyWith(
        selectedCamera: _cameraService.selectedCamera,
        isTorchOn: _cameraService.flashOnOff,
        flashMode: _cameraService.flashMode,
        isInitialized: _cameraService.isInitialized,
        cameraController: _cameraService.cameraController,
      ));
    } catch (e) {
      emit(CameraError('Failed to switch the camera: $e'));
    }
  }

  Future<void> toggleFlash() async {
    try {
      await _cameraService.toggleFlashMode();

      final currentConfig = state as CameraConfiguration;
      emit(currentConfig.copyWith(
        selectedCamera: _cameraService.selectedCamera,
        isTorchOn: _cameraService.flashOnOff,
        flashMode: _cameraService.flashMode,
        isInitialized: _cameraService.isInitialized,
        cameraController: _cameraService.cameraController,
      ));
    } catch (e) {
      emit(CameraError('Failed to toggle flash: $e'));
    }
  }

  Future<void> takePicture() async {
    if (state is! CameraConfiguration) return;
    final currentConfig = state as CameraConfiguration;

    try {
      final result = await _cameraService.takePicture();

      result.fold(
        (error) =>
            emit(CameraError('Failed to capture photo: ${error.description}')),
        (picture) {
          emit(PhotoCaptured(
            image: picture,
            config: currentConfig,
          ));
          // Emit back the configuration state after photo is handled
          emit(currentConfig);
        },
      );
    } catch (e) {
      emit(CameraError('Failed to capture photo: $e'));
      // Emit back the configuration state after error
      emit(currentConfig);
    }
  }

  Future<void> setZoom(double zoomLevel) async {
    try {
      await _cameraService.setZoom(zoomLevel);

      final currentConfig = state as CameraConfiguration;
      emit(currentConfig.copyWith(
        selectedCamera: _cameraService.selectedCamera,
        isTorchOn: _cameraService.flashOnOff,
        flashMode: _cameraService.flashMode,
        isInitialized: _cameraService.isInitialized,
        cameraController: _cameraService.cameraController,
      ));
    } catch (e) {
      emit(CameraError('Failed to switch the camera: $e'));
    }
  }

  void disposeCamera() {}

  void updateTimer(int seconds) {
    if (state is CameraConfiguration) {
      final currentConfig = state as CameraConfiguration;
      emit(currentConfig.copyWith(selectedTimer: seconds));
    }
  }
}
