part of 'camera_cubit.dart';

@immutable
sealed class CameraState {}

final class Camera<PERSON>rror extends CameraState {
  final String message;
  CameraError(this.message);
}

final class CameraConfiguration extends CameraState {
  final SelectedCamera selectedCamera;
  final bool isTorchOn;
  final CameraFlashMode flashMode;
  final bool isInitialized;
  final CameraController? cameraController;
  final int selectedTimer;

  CameraConfiguration({
    required this.selectedCamera,
    required this.isTorchOn,
    required this.flashMode,
    required this.isInitialized,
    required this.cameraController,
    this.selectedTimer = 0,
  });

  CameraConfiguration copyWith({
    SelectedCamera? selectedCamera,
    bool? isTorchOn,
    CameraFlashMode? flashMode,
    bool? isInitialized,
    CameraController? cameraController,
    int? selectedTimer,
  }) {
    return CameraConfiguration(
      selectedCamera: selectedCamera ?? this.selectedCamera,
      isTorchOn: isTorchOn ?? this.isTorchOn,
      flashMode: flashMode ?? this.flashMode,
      isInitialized: isInitialized ?? this.isInitialized,
      cameraController: cameraController ?? this.cameraController,
      selectedTimer: selectedTimer ?? this.selectedTimer,
    );
  }
}

final class PhotoCaptured extends CameraState {
  final XFile image;
  final CameraConfiguration config;

  PhotoCaptured({
    required this.image,
    required this.config,
  });
}
