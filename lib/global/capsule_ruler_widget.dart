import 'package:flutter/material.dart';
import 'dart:math';
import 'package:flutter/services.dart';

/// A ruler widget with a fixed capsule indicator and center-aligned ticks.
///
/// This widget provides a customizable ruler with center-aligned major and medium ticks,
/// and a fixed capsule-shaped indicator positioned in the center. The ruler moves
/// horizontally underneath the fixed capsule indicator.
///
/// Features:
/// - Customizable tick marks, colors, and dimensions
/// - Fixed capsule indicator with moving ruler
/// - Center-aligned ticks
/// - Automatic scaling based on available space
/// - Snapping to tick marks with animation
/// - RTL support
/// - Optional widgets at the start and end of the ruler
class CapsuleRuler extends StatefulWidget {
  /// Current value of the ruler.
  final double value;

  /// Height of the ruler widget.
  final double height;

  /// Optional width of the ruler widget. If not provided, calculated based on height.
  final double? width;

  /// Background color of the ruler.
  final Color backgroundColor;

  /// Color of the tick marks.
  final Color tickColor;

  /// Border color for tick marks. If null, no border is drawn.
  final Color? tickBorderColor;

  /// Border width for tick marks.
  final double tickBorderWidth;

  /// Border radius for tick marks. If null, half of the tick width is used.
  final double? tickBorderRadius;

  /// Width of major tick marks.
  final double majorTickWidth;

  /// Height of major tick marks as a percentage of the ruler height (0.0 to 1.0).
  final double majorTickHeightRatio;

  /// Width of medium tick marks.
  final double mediumTickWidth;

  /// Height of medium tick marks as a percentage of the ruler height (0.0 to 1.0).
  final double mediumTickHeightRatio;

  /// Width of minor tick marks.
  final double minorTickWidth;

  /// Height of minor tick marks as a percentage of the ruler height (0.0 to 1.0).
  final double minorTickHeightRatio;

  /// Color of the capsule indicator.
  final Color capsuleColor;

  /// Border color for the capsule. If null, no border is drawn.
  final Color? capsuleBorderColor;

  /// Border width for the capsule.
  final double capsuleBorderWidth;

  /// Background color for the capsule. If null, capsuleColor is used.
  final Color? capsuleBackgroundColor;

  /// Shadow color for the capsule. If null, no shadow is drawn.
  final Color? capsuleShadowColor;

  /// Shadow elevation for the capsule.
  final double capsuleShadowElevation;

  /// Opacity of the background (0.0 to 1.0).
  final double backgroundOpacity;

  /// Radius of the glow effect around the ruler.
  final double glowRadius;

  /// Color of the glow effect.
  final Color glowColor;

  /// Number of ticks visible in the ruler viewport.
  final int visibleTicks;

  /// Number of minor ticks between major ticks.
  final int minorTicksPerMajor;

  /// Interval between major ticks (e.g., 5 for 0,5,10,15 or 2 for 0,2,4,6).
  final int majorTickInterval;

  /// Width percentage of the ruler within the container (0.0 to 1.0).
  /// A value of 0.9 means the ruler takes 90% of the container width.
  final double rulerWidthFactor;

  /// Whether to force the ruler to take the full width regardless of the value range.
  /// When true, the ruler will always span the full width, even with large value ranges.
  final bool forceFullWidth;

  /// The reference range to use when forceFullWidth is true.
  /// This determines how compressed or expanded the ticks appear.
  /// A smaller value makes ticks more spread out, a larger value makes them more compressed.
  /// Default is 20.0 (equivalent to a range from -10 to 10).
  final double referenceRange;

  // Baseline functionality has been removed from CapsuleRuler

  /// Corner radius of the ruler container.
  final double cornerRadius;

  /// Width of the capsule indicator.
  final double capsuleWidth;

  /// Height of the capsule indicator.
  final double capsuleHeight;

  /// Spacing between the top of the container and the capsule.
  final double capsuleTopSpacing;

  /// Vertical padding inside the ruler container.
  final double rulerPadding;

  /// Horizontal padding inside the ruler container.
  final double horizontalPadding;

  /// Whether to enable snapping to the nearest tick.
  final bool enableSnapping;

  /// Minimum value of the ruler.
  final double minValue;

  /// Maximum value of the ruler.
  final double maxValue;

  /// Callback when the value changes.
  final ValueChanged<double>? onChanged;

  /// Interval for snapping (e.g., 0.5 for half-ticks, 0.25 for quarter-ticks).
  final double snapInterval;

  /// Whether to show numeric labels on major ticks.
  final bool showLabels;

  /// Text style for the numeric labels.
  final TextStyle? labelStyle;

  /// Function to format the label text. If null, the tick value is displayed as is.
  final String Function(int value)? labelFormatter;

  /// Duration of the snap animation.
  final Duration snapAnimationDuration;

  /// Whether to automatically adapt to smaller spaces by hiding labels and simplifying the ruler.
  final bool autoAdaptToSpace;

  /// Whether to automatically hide labels when the ruler is scaled below a certain width.
  final bool autoHideLabels;

  /// The width threshold below which labels are automatically hidden if autoHideLabels is true.
  final double labelHideThreshold;

  /// The width threshold below which minor ticks are hidden for better readability in small spaces.
  final double minorTickHideThreshold;

  /// Whether to show only major ticks with alternating sizes (one large, one medium).
  /// When true, minor ticks are not shown regardless of minorTicksPerMajor value.
  final bool showAlternatingMajorTicks;

  /// Optional widget to display at the start (left side) of the ruler.
  /// This will be positioned at the left edge of the ruler container.
  final Widget? startWidget;

  /// Optional widget to display at the end (right side) of the ruler.
  /// This will be positioned at the right edge of the ruler container.
  final Widget? endWidget;

  const CapsuleRuler({
    super.key,
    required this.value,
    this.height = 40,
    this.width,
    this.backgroundColor = Colors.black,
    this.tickColor = Colors.white,
    this.tickBorderColor,
    this.tickBorderWidth = 0.5,
    this.tickBorderRadius,
    this.majorTickWidth = 2.0,
    this.majorTickHeightRatio = 0.4,
    this.mediumTickWidth = 1.5,
    this.mediumTickHeightRatio = 0.25,
    this.minorTickWidth = 1.0,
    this.minorTickHeightRatio = 0.15,
    this.capsuleColor = Colors.white,
    this.capsuleBorderColor,
    this.capsuleBorderWidth = 0.5,
    this.capsuleBackgroundColor,
    this.capsuleShadowColor,
    this.capsuleShadowElevation = 0.0,
    this.backgroundOpacity = 0.7,
    this.glowRadius = 8.0,
    this.glowColor = Colors.white,
    this.visibleTicks = 21,
    this.minorTicksPerMajor = 4,
    this.majorTickInterval = 5,
    this.rulerWidthFactor = 1.0, // Default to 100% width
    this.forceFullWidth = false, // Default to false
    this.referenceRange = 20.0, // Default to 20.0 (range from -10 to 10)
    this.cornerRadius = 8.0,
    this.capsuleWidth = 8, // Narrower width since it's full height
    this.capsuleHeight = 24, // This is not used anymore as we're using full height
    this.capsuleTopSpacing = 4.0, // This is not used anymore as we're positioning at the top padding
    this.rulerPadding = 4.0,
    this.horizontalPadding = 8.0,
    this.enableSnapping = true,
    this.minValue = -10.0,
    this.maxValue = 10.0,
    this.onChanged,
    this.snapInterval = 1.0,
    this.showLabels = true,
    this.labelStyle,
    this.labelFormatter,
    this.snapAnimationDuration = const Duration(milliseconds: 200),
    this.autoAdaptToSpace = true,
    this.autoHideLabels = true,
    this.labelHideThreshold = 200.0,
    this.minorTickHideThreshold = 100.0,
    this.showAlternatingMajorTicks = false,
    this.startWidget,
    this.endWidget,
  })  : assert(snapInterval > 0, 'Snap interval must be greater than 0'),
        assert(rulerWidthFactor > 0 && rulerWidthFactor <= 1.0,
            'Ruler width factor must be between 0 and 1.0');

  @override
  State<CapsuleRuler> createState() => _CapsuleRulerState();
}

class _CapsuleRulerState extends State<CapsuleRuler>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;
  late double _tickSpacing;
  late double _startValue;
  late double _endValue;

  @override
  void initState() {
    super.initState();

    // Initialize animation controller for snapping
    _animationController = AnimationController(
      vsync: this,
      duration: widget.snapAnimationDuration,
    );

    _animation = Tween<double>(begin: widget.value, end: widget.value).animate(
        CurvedAnimation(
            parent: _animationController, curve: Curves.easeOutCubic))
      ..addListener(() {
        if (widget.onChanged != null) {
          widget.onChanged!(_animation.value);
        }
      });

    // Initial tick spacing will be calculated in build method
    _tickSpacing = 0;
  }

  @override
  void didUpdateWidget(CapsuleRuler oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Update animation duration if it changes
    if (oldWidget.snapAnimationDuration != widget.snapAnimationDuration) {
      _animationController.duration = widget.snapAnimationDuration;
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final containerHeight = widget.height;
    final isRtl = Directionality.of(context) == TextDirection.rtl;

    // Use LayoutBuilder to get the available width from the parent container
    return LayoutBuilder(
      builder: (context, constraints) {
        // Use the available width from the parent container if width is not provided
        final containerWidth = widget.width ?? constraints.maxWidth;

        // Determine size category based on available width for auto-adaptation
        final bool isVerySmall = containerWidth < widget.minorTickHideThreshold;
        final bool isSmall =
            containerWidth < widget.labelHideThreshold && !isVerySmall;

        // Calculate effective parameters based on available space
        final effectiveShowLabels = widget.autoHideLabels &&
                widget.autoAdaptToSpace &&
                (isSmall || isVerySmall)
            ? false
            : widget.showLabels;

        // Adjust minor ticks for small spaces if auto-adaptation is enabled
        final effectiveMinorTicksPerMajor =
            widget.autoAdaptToSpace && isVerySmall
                ? 0
                : widget.minorTicksPerMajor;

        // Calculate a scaling factor based on container width
        double scaleFactor;

        // Simplified scaling factor calculation
        if (widget.autoAdaptToSpace) {
          if (isVerySmall) {
            scaleFactor = 0.5; // Very small scale for tiny containers
          } else if (isSmall) {
            scaleFactor = 0.7; // Smaller scale for small containers
          } else if (containerWidth < 400) {
            scaleFactor = 0.9; // Slightly reduced scale for medium containers
          } else if (containerWidth > 800) {
            scaleFactor = 1.2; // Larger scale for very large containers
          } else {
            scaleFactor = 1.0; // Default scale
          }
        } else {
          // Map container width to scale factor using a more continuous approach
          if (containerWidth <= 200) {
            scaleFactor = 0.7;
          } else if (containerWidth <= 300) {
            scaleFactor = 0.8;
          } else if (containerWidth <= 400) {
            scaleFactor = 0.9;
          } else if (containerWidth >= 800) {
            scaleFactor = 1.2;
          } else {
            scaleFactor = 1.0;
          }
        }

        // Calculate the effective horizontal padding
        final effectiveHorizontalPadding =
            widget.horizontalPadding * scaleFactor;

        // Calculate the available width after padding
        final availableWidth =
            containerWidth - (effectiveHorizontalPadding * 2);

        // Recalculate tick spacing based on the available width and the range of values
        final valueRange = widget.maxValue - widget.minValue;
        if (valueRange > 0) {
          // Calculate the spacing to distribute ticks evenly across the available width
          if (widget.forceFullWidth) {
            // When forceFullWidth is true, we use the specified reference range
            // This ensures consistent spacing regardless of the actual value range
            _tickSpacing = availableWidth / widget.referenceRange;
          } else {
            // Use the exact value range to ensure the ruler spans the entire available width
            _tickSpacing = availableWidth / valueRange;
          }

          // Ensure minimum spacing between ticks for readability
          _tickSpacing = _tickSpacing.clamp(5.0 * scaleFactor, double.infinity);
        }

        // Calculate scaled dimensions for the capsule and other elements
        final effectiveCapsuleWidth = widget.capsuleWidth * scaleFactor;
        final effectiveCapsuleHeight = widget.capsuleHeight * scaleFactor;
        final effectiveCapsuleTopSpacing =
            widget.capsuleTopSpacing * scaleFactor;
        final effectiveRulerPadding = widget.rulerPadding * scaleFactor;
        final effectiveCornerRadius = widget.cornerRadius * scaleFactor;
        final effectiveGlowRadius = widget.glowRadius * scaleFactor;

        // Adjust text style for labels based on available space
        final effectiveLabelStyle = isSmall || isVerySmall
            ? (widget.labelStyle?.copyWith(fontSize: isVerySmall ? 6 : 8) ??
                TextStyle(
                    fontSize: isVerySmall ? 6 : 8, color: widget.tickColor))
            : widget.labelStyle;

        return SizedBox(
          height: containerHeight,
          width: containerWidth,
          child: Stack(
            clipBehavior: Clip.none,
            children: [
              // Background with glow
              Container(
                height: containerHeight,
                width: containerWidth,
                decoration: BoxDecoration(
                  //gradient top white to bottom black
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.white.withAlpha(128), // 0.50 opacity
                      Colors.black.withAlpha(128), // 0.50 opacity
                      Colors.black.withAlpha(204), // 0.80 opacity
                    ],
                    stops: const [0.0, 0.3, 1.0],
                  ),
                    borderRadius: BorderRadius.circular(effectiveCornerRadius),
                    boxShadow: [
                      BoxShadow(
                        color: widget.glowColor.withAlpha(76), // 0.3 opacity
                        blurRadius: effectiveGlowRadius,
                        spreadRadius: 1,
                      ),
                    ],
                    border: Border.all(
                      color: Colors.white,
                      width: 0.8,
                    )),
              ),

              // Moving ruler with fixed capsule
              _buildMovingRulerFixedCapsule(
                containerHeight,
                containerWidth,
                effectiveMinorTicksPerMajor,
                effectiveShowLabels,
                effectiveLabelStyle,
                effectiveCapsuleWidth,
                effectiveCapsuleHeight,
                effectiveCapsuleTopSpacing,
                effectiveRulerPadding,
                scaleFactor,
              ),

              // Start widget (positioned at the left or right depending on RTL)
              if (widget.startWidget != null)
                Positioned(
                  left: isRtl ? null : effectiveHorizontalPadding,
                  right: isRtl ? effectiveHorizontalPadding : null,
                  top: 0,
                  bottom: 0,
                  child: Center(
                    child: widget.startWidget!,
                  ),
                ),

              // End widget (positioned at the right or left depending on RTL)
              if (widget.endWidget != null)
                Positioned(
                  right: isRtl ? null : effectiveHorizontalPadding,
                  left: isRtl ? effectiveHorizontalPadding : null,
                  top: 0,
                  bottom: 0,
                  child: Center(
                    child: widget.endWidget!,
                  ),
                ),

              // Gesture detector for interaction
              if (widget.onChanged != null)
                Positioned(
                  // Position the gesture detector to cover the ruler area
                  // but leave space for the start and end widgets if they exist
                  left: widget.startWidget != null ? effectiveHorizontalPadding * 3 : 0,
                  right: widget.endWidget != null ? effectiveHorizontalPadding * 3 : 0,
                  top: 0,
                  bottom: 0,
                  child: GestureDetector(
                    behavior: HitTestBehavior.opaque,
                    onHorizontalDragUpdate: (details) {
                      // Apply RTL adjustment if needed
                      final adjustedDelta =
                          isRtl ? -details.delta.dx : details.delta.dx;
                      final delta = adjustedDelta / _tickSpacing;

                      // Calculate new value (moving ruler, so negative delta)
                      final newValue = widget.value - delta;

                      // Ensure the value stays within bounds
                      final clampedValue = newValue.clamp(widget.minValue, widget.maxValue);

                      // Only update if the value has changed
                      if (widget.onChanged != null && clampedValue != widget.value) {
                        widget.onChanged!(clampedValue);
                      }
                    },
                    onHorizontalDragEnd: widget.enableSnapping
                        ? (_) {
                            // Snap to nearest tick when drag ends
                            _snapToNearestTick();
                          }
                        : null,
                    // Make the gesture detector fill the available space
                    child: Container(
                      color: Colors.transparent,
                    ),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }

  // Snap to the nearest tick with animation
  void _snapToNearestTick() {
    if (widget.onChanged == null) return;

    // Calculate the nearest tick value based on snap interval
    final nearestValue =
        (widget.value / widget.snapInterval).round() * widget.snapInterval;

    // Ensure the value stays within the min/max range
    final clampedValue = nearestValue.clamp(widget.minValue, widget.maxValue);

    // Only snap if we're not already at a tick
    if ((widget.value - clampedValue).abs() > 0.01) {
      // Set up animation
      _startValue = widget.value;
      _endValue = clampedValue;

      _animation = Tween<double>(begin: _startValue, end: _endValue).animate(
          CurvedAnimation(
              parent: _animationController, curve: Curves.easeOutCubic));

      // Reset and start animation
      _animationController.reset();
      _animationController.forward();

      // Provide haptic feedback on major ticks
      if (clampedValue % widget.majorTickInterval == 0) {
        HapticFeedback.lightImpact();
      }
    }
  }

  Widget _buildMovingRulerFixedCapsule(
    double containerHeight,
    double containerWidth,
    int effectiveMinorTicksPerMajor,
    bool effectiveShowLabels,
    TextStyle? effectiveLabelStyle,
    double effectiveCapsuleWidth,
    double effectiveCapsuleHeight,
    double effectiveCapsuleTopSpacing,
    double effectiveRulerPadding,
    double scaleFactor,
  ) {
    // Clamp the value to ensure the ruler stops at min/max
    final clampedValue = widget.value.clamp(widget.minValue, widget.maxValue);

    // Calculate the effective horizontal padding
    final effectiveHorizontalPadding = widget.horizontalPadding * scaleFactor;

    // Calculate the available width after padding
    final availableWidth = containerWidth - (effectiveHorizontalPadding * 2);

    // Calculate the effective height for the ruler content
    final effectiveHeight = containerHeight - (effectiveRulerPadding * 2);

    // Calculate tick dimensions based on scale factor and customization parameters
    final longTickHeight = effectiveHeight * widget.majorTickHeightRatio * scaleFactor;
    final mediumTickHeight = effectiveHeight * widget.mediumTickHeightRatio * scaleFactor;
    final shortTickHeight = effectiveHeight * widget.minorTickHeightRatio * scaleFactor;

    // Calculate tick widths based on scale factor and customization parameters
    final majorTickWidth = widget.majorTickWidth * scaleFactor;
    final mediumTickWidth = widget.mediumTickWidth * scaleFactor;
    final minorTickWidth = widget.minorTickWidth * scaleFactor;

    // Calculate the tick spacing to distribute ticks evenly across the available width
    final valueRange = widget.maxValue - widget.minValue;
    if (valueRange <= 0) return Container(); // Safety check

    // Calculate the spacing between ticks
    double tickSpacing;
    if (widget.forceFullWidth) {
      // When forceFullWidth is true, use the specified reference range
      tickSpacing = availableWidth / widget.referenceRange;
    } else {
      // Use the exact value range to ensure the ruler spans the entire available width
      tickSpacing = availableWidth / valueRange;
    }

    // Ensure minimum spacing between ticks for readability
    tickSpacing = tickSpacing.clamp(5.0 * scaleFactor, double.infinity);

    // Update the stored tick spacing for use in gesture calculations
    _tickSpacing = tickSpacing;

    // Calculate the total width of the ruler in pixels
    final totalRulerWidth = valueRange * tickSpacing;

    // Calculate the center position of the container
    final centerX = availableWidth / 2;

    // Calculate the position of the minimum value
    final minValueX = centerX - (totalRulerWidth / 2);

    // Calculate the offset based on the current value, ensuring perfect alignment with the capsule
    double valueOffset;

    // For perfect alignment, we need to calculate where the current value's tick should be
    // and position it exactly at the center of the available width
    final currentValuePosition = minValueX + ((clampedValue - widget.minValue) * tickSpacing);

    // The offset is the difference between where the current value's tick is
    // and where it should be (exact center of the available width)
    // We use round to ensure pixel-perfect alignment
    valueOffset = (currentValuePosition - centerX).roundToDouble();

    // Generate tick widgets
    List<Widget> generateTicks() {
      final ticks = <Widget>[];

      // Calculate the range of ticks to display
      final startValue = widget.minValue;
      final endValue = widget.maxValue;

      // Calculate the visible range of ticks based on the container width
      // This ensures we only render ticks that could be visible
      final visibleRangeHalf = (availableWidth / 2 / tickSpacing).ceil() + 2; // Add a small buffer

      // Calculate the visible range of values based on the current value
      final minVisibleValue = max(startValue, clampedValue - visibleRangeHalf);
      final maxVisibleValue = min(endValue, clampedValue + visibleRangeHalf);

      // Generate ticks for the visible range
      for (int i = minVisibleValue.floor(); i <= maxVisibleValue.ceil(); i++) {
        // Skip values outside our range
        if (i < startValue || i > endValue) continue;

        // Determine tick type
        bool isLongTick;
        bool isMediumTick;

        if (widget.showAlternatingMajorTicks) {
          // Alternating major ticks mode
          final isMajorTick = i % widget.majorTickInterval == 0;
          if (!isMajorTick) continue; // Skip non-major ticks

          // Alternate between long and medium ticks
          final majorTickIndex = i ~/ widget.majorTickInterval;
          isLongTick = majorTickIndex % 2 == 0;
          isMediumTick = !isLongTick;
        } else {
          // Standard mode
          isLongTick = i % widget.majorTickInterval == 0;
          isMediumTick = !isLongTick && i % 2 == 0;
        }

        // Determine tick height and width
        final tickHeight = isLongTick
            ? longTickHeight
            : (isMediumTick ? mediumTickHeight : shortTickHeight);

        final tickWidth = isLongTick
            ? majorTickWidth
            : (isMediumTick ? mediumTickWidth : minorTickWidth);

        // Calculate position relative to the minimum value position
        final x = minValueX + ((i - widget.minValue) * tickSpacing);

        // Calculate the actual position after applying the value offset
        // This ensures the current value's tick is perfectly centered under the capsule
        // We need to account for the tick width to center it properly
        // Subtract half the tick width to center the tick under the capsule
        final adjustedX = (x - valueOffset).roundToDouble() - (tickWidth / 2);

        // Skip ticks that would be outside the visible area
        if (adjustedX < -tickWidth || adjustedX > availableWidth + tickWidth) continue;

        // Create tick container
        ticks.add(
          Positioned(
            left: adjustedX, // Position based on current value
            top: (effectiveHeight - tickHeight) / 2, // Center vertically
            child: Container(
              width: tickWidth,
              height: tickHeight,
              decoration: BoxDecoration(
                color: widget.tickColor,
                borderRadius: BorderRadius.circular(
                  widget.tickBorderRadius ?? (tickWidth / 2),
                ),
                border: widget.tickBorderColor != null
                    ? Border.all(
                        color: widget.tickBorderColor!,
                        width: widget.tickBorderWidth,
                      )
                    : null,
              ),
            ),
          ),
        );

        // Add label for major ticks
        if (effectiveShowLabels && isLongTick) {
          final labelText = widget.labelFormatter != null
              ? widget.labelFormatter!(i)
              : i.toString();

          ticks.add(
            Positioned(
              left: adjustedX, // Position based on current value
              bottom: 2, // Position at bottom
              child: Text(
                labelText,
                style: effectiveLabelStyle ?? TextStyle(
                  fontSize: effectiveHeight * 0.25 * scaleFactor,
                  color: widget.tickColor,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          );
        }

        // Add minor ticks between major ticks
        if (isLongTick && i < endValue && !widget.showAlternatingMajorTicks && effectiveMinorTicksPerMajor > 0) {
          for (int j = 1; j <= effectiveMinorTicksPerMajor; j++) {
            // Skip if outside boundaries
            final minorTickValue = i + j / (effectiveMinorTicksPerMajor + 1);
            if (minorTickValue < startValue || minorTickValue > endValue) continue;

            // Determine if this is a medium minor tick (middle one)
            final isMinorMedium = j == (effectiveMinorTicksPerMajor + 1) ~/ 2;

            // Determine height and width
            final minorTickHeight = isMinorMedium ? mediumTickHeight : shortTickHeight;
            // Use a different variable name to avoid conflict
            final tickWidthForMinor = isMinorMedium ? mediumTickWidth : minorTickWidth;

            // Calculate position relative to the current major tick
            final minorX = minValueX + ((i + j / (effectiveMinorTicksPerMajor + 1)) - widget.minValue) * tickSpacing;

            // Calculate the actual position after applying the value offset
            // This ensures the minor ticks are perfectly aligned with the major ticks
            // We need to account for the tick width to center it properly
            // Subtract half the tick width to center the tick under the capsule
            final adjustedMinorX = (minorX - valueOffset).roundToDouble() - (tickWidthForMinor / 2);

            // Skip minor ticks that would be outside the visible area
            if (adjustedMinorX < -tickWidthForMinor || adjustedMinorX > availableWidth + tickWidthForMinor) continue;

            // Create minor tick container
            ticks.add(
              Positioned(
                left: adjustedMinorX, // Position based on current value
                top: (effectiveHeight - minorTickHeight) / 2, // Center vertically
                child: Container(
                  width: tickWidthForMinor,
                  height: minorTickHeight,
                  decoration: BoxDecoration(
                    color: widget.tickColor,
                    borderRadius: BorderRadius.circular(
                      widget.tickBorderRadius ?? (tickWidthForMinor / 2),
                    ),
                    border: widget.tickBorderColor != null
                        ? Border.all(
                            color: widget.tickBorderColor!,
                            width: widget.tickBorderWidth,
                          )
                        : null,
                  ),
                ),
              ),
            );
          }
        }
      }

      return ticks;
    }

    return Stack(
      children: [
        // Ticks that move - full width with padding
        Padding(
          padding: EdgeInsets.only(
            top: effectiveRulerPadding,
            bottom: effectiveRulerPadding,
            left: effectiveHorizontalPadding,
            right: effectiveHorizontalPadding,
          ),
          child: SizedBox(
            width: availableWidth,
            height: effectiveHeight,
            child: ClipRect(
              child: Stack(
                clipBehavior: Clip.none,
                children: generateTicks(),
              ),
            ),
          ),
        ),

        // Fixed capsule in center - full height
        // Use exact positioning to ensure perfect alignment with ticks
        Positioned(
          top: effectiveRulerPadding,
          bottom: effectiveRulerPadding,
          // Position exactly at center with precise calculations
          // Account for horizontal padding to ensure perfect alignment with ticks
          left: effectiveHorizontalPadding + (availableWidth - effectiveCapsuleWidth) / 2,
          width: effectiveCapsuleWidth,
          child: Container(
            height: effectiveHeight,
            decoration: BoxDecoration(
              color: widget.capsuleBackgroundColor ?? widget.capsuleColor,
              borderRadius: BorderRadius.circular(effectiveCapsuleWidth / 2),
              border: widget.capsuleBorderColor != null
                  ? Border.all(
                      color: widget.capsuleBorderColor!,
                      width: widget.capsuleBorderWidth,
                    )
                  : null,
              boxShadow: widget.capsuleShadowColor != null && widget.capsuleShadowElevation > 0
                  ? [
                      BoxShadow(
                        color: widget.capsuleShadowColor!,
                        blurRadius: widget.capsuleShadowElevation * 2,
                        spreadRadius: widget.capsuleShadowElevation / 2,
                      ),
                    ]
                  : null,
            ),
          ),
        ),
      ],
    );
  }
}

/// Custom painter for drawing the ruler with center-aligned ticks
class CenterAlignedRulerPainter extends CustomPainter {
  final double value;
  final Color tickColor;
  final int visibleTicks;
  final int minorTicksPerMajor;
  final int majorTickInterval;
  final double minValue;
  final double maxValue;
  final bool showLabels;
  final TextStyle? labelStyle;
  final double tickSpacing;
  final double scaleFactor;
  final String Function(int value)? labelFormatter;
  final double rulerWidthFactor;

  final bool forceFullWidth;
  final double referenceRange;
  // Baseline functionality has been removed from CapsuleRuler
  final bool showAlternatingMajorTicks;

  CenterAlignedRulerPainter({
    required this.value,
    required this.tickColor,
    required this.visibleTicks,
    this.minorTicksPerMajor = 4,
    this.majorTickInterval = 5,
    this.minValue = -10.0,
    this.maxValue = 10.0,
    this.showLabels = true,
    this.labelStyle,
    required this.tickSpacing,
    this.scaleFactor = 1.0,
    this.labelFormatter,
    this.rulerWidthFactor = 1.0,
    this.forceFullWidth = false,
    this.referenceRange = 20.0,
    this.showAlternatingMajorTicks = false,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = tickColor
      ..strokeCap = StrokeCap.round
      ..style = PaintingStyle.stroke;

    final height = size.height;
    // Apply scale factor to tick heights
    final longTickHeight = height * 0.4 * scaleFactor;
    final mediumTickHeight = height * 0.25 * scaleFactor;
    final shortTickHeight = height * 0.15 * scaleFactor;

    // Center line for ticks (vertical center of the ruler)
    final centerY = height / 2;

    // Baseline functionality has been removed from CapsuleRuler

    // Calculate the actual width of the ruler based on the width factor
    final rulerWidth = size.width * rulerWidthFactor;

    // Calculate the tick spacing to ensure the ruler spans the specified width percentage
    // For moving ruler, we need to adjust based on the current value
    double adjustedTickSpacing;
    if (forceFullWidth) {
      // When forceFullWidth is true, we use the specified reference range
      // This ensures consistent spacing regardless of the actual value range
      adjustedTickSpacing = rulerWidth / referenceRange;
    } else {
      adjustedTickSpacing = rulerWidth / (maxValue - minValue);
    }

    // Set up text painter for labels
    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.center,
    );

    // Apply scale factor to text size
    final textStyle = labelStyle ??
        TextStyle(
          fontSize: height * 0.25 * scaleFactor,
          color: tickColor,
          fontWeight: FontWeight.bold,
        );

    // Calculate visible range based on current value
    final half = visibleTicks ~/ 2;
    final startTick = value.toInt() - half;
    final endTick = value.toInt() + half;

    // Baseline functionality has been removed from CapsuleRuler

    // Calculate the position of the minimum value on the ruler
    // (Same calculation as used for the baseline)
    final valueRange = maxValue - minValue;
    final valueRatio = (value - minValue) / valueRange;
    final centerX = size.width / 2;
    final totalRulerWidth = valueRange * adjustedTickSpacing;
    final minValueX = centerX - (valueRatio * totalRulerWidth);

    // Calculate visible tick range based on screen coordinates
    // This optimization ensures we only process ticks that could be visible
    final minVisibleValue = max(minValue, startTick.toDouble());
    final maxVisibleValue = min(maxValue, endTick.toDouble());

    // Pre-calculate tick properties for better performance
    final majorTickStrokeWidth = 2.0 * scaleFactor;
    final mediumTickStrokeWidth = 1.5 * scaleFactor;
    final minorTickStrokeWidth = 1.0 * scaleFactor;

    // Draw ticks within the visible range
    for (int i = minVisibleValue.floor(); i <= maxVisibleValue.ceil(); i++) {
      // Skip values outside our range
      if (i < minValue || i > maxValue) continue;

      final tickValue = i.toDouble();

      // Calculate x position
      final x = minValueX + ((tickValue - minValue) * adjustedTickSpacing);

      // Skip ticks outside the visible area (optimization)
      if (x < -10 || x > size.width + 10) continue;

      // Determine tick type
      bool isLongTick;
      bool isMediumTick;

      if (showAlternatingMajorTicks) {
        // Alternating major ticks mode
        final isMajorTick = i % majorTickInterval == 0;
        if (!isMajorTick) continue; // Skip non-major ticks

        // Alternate between long and medium ticks
        final majorTickIndex = i ~/ majorTickInterval;
        isLongTick = majorTickIndex % 2 == 0;
        isMediumTick = !isLongTick;
      } else {
        // Standard mode
        isLongTick = i % majorTickInterval == 0;
        isMediumTick = !isLongTick && i % 2 == 0;
      }

      // Set stroke width based on tick type
      paint.strokeWidth = isLongTick
          ? majorTickStrokeWidth
          : (isMediumTick ? mediumTickStrokeWidth : minorTickStrokeWidth);

      // Determine tick height
      final tickHeight = isLongTick
          ? longTickHeight
          : (isMediumTick ? mediumTickHeight : shortTickHeight);

      // Draw the tick centered vertically
      canvas.drawLine(
        Offset(x, centerY - tickHeight / 2),
        Offset(x, centerY + tickHeight / 2),
        paint,
      );

      // Draw label for major ticks
      if (showLabels && isLongTick) {
        final labelText = labelFormatter != null ? labelFormatter!(i) : i.toString();
        textPainter.text = TextSpan(
          text: labelText,
          style: textStyle,
        );

        textPainter.layout();
        // Position labels at the bottom of the ruler
        final labelY = height - textPainter.height - 2;
        textPainter.paint(
          canvas,
          Offset(x - (textPainter.width / 2), labelY),
        );
      }

      // Draw minor ticks between major ticks
      if (isLongTick && i < maxVisibleValue && !showAlternatingMajorTicks && minorTicksPerMajor > 0) {
        final minorTickSpacing = adjustedTickSpacing / (minorTicksPerMajor + 1);

        for (int j = 1; j <= minorTicksPerMajor; j++) {
          final minorTickValue = i + j / (minorTicksPerMajor + 1);

          // Skip if outside boundaries
          if (minorTickValue < minValue || minorTickValue > maxValue) continue;

          // Calculate minor tick position
          final minorX = x + (j * minorTickSpacing);

          // Skip if outside visible area
          if (minorX < 0 || minorX > size.width) continue;

          // Determine if this is a medium minor tick (middle one)
          final isMinorMedium = j == (minorTicksPerMajor + 1) ~/ 2;

          // Set stroke width
          paint.strokeWidth = isMinorMedium ? mediumTickStrokeWidth : minorTickStrokeWidth;

          // Determine height
          final minorTickHeight = isMinorMedium ? mediumTickHeight : shortTickHeight;

          // Draw the minor tick centered vertically
          canvas.drawLine(
            Offset(minorX, centerY - minorTickHeight / 2),
            Offset(minorX, centerY + minorTickHeight / 2),
            paint,
          );
        }
      }
    }
  }

  @override
  bool shouldRepaint(CenterAlignedRulerPainter oldDelegate) {
    // Only check properties that would visually affect the painting
    return oldDelegate.value != value ||
        oldDelegate.tickSpacing != tickSpacing ||
        oldDelegate.tickColor != tickColor ||
        oldDelegate.showLabels != showLabels ||
        oldDelegate.showAlternatingMajorTicks != showAlternatingMajorTicks ||
        // Check dimension-related properties
        oldDelegate.minorTicksPerMajor != minorTicksPerMajor ||
        oldDelegate.majorTickInterval != majorTickInterval ||
        oldDelegate.minValue != minValue ||
        oldDelegate.maxValue != maxValue ||
        oldDelegate.scaleFactor != scaleFactor ||
        oldDelegate.rulerWidthFactor != rulerWidthFactor ||
        oldDelegate.forceFullWidth != forceFullWidth ||
        oldDelegate.referenceRange != referenceRange;
  }
}

/// Custom painter for drawing the capsule-shaped indicator
class CapsulePainter extends CustomPainter {
  final Color color;

  CapsulePainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final w = size.width;
    final h = size.height;
    final radius = w / 2; // Capsule has semicircle ends, so radius is half the width

    // Create capsule path (rounded rectangle with semicircle ends)
    final path = Path()
      ..addRRect(RRect.fromRectAndRadius(
        Rect.fromLTWH(0, 0, w, h),
        Radius.circular(radius),
      ));

    // Fill shape
    canvas.drawPath(path, paint);

    // Stroke border with slightly transparent version of the same color
    paint
      ..style = PaintingStyle.stroke
      ..strokeWidth = w < 10 ? 0.5 : 1.0
      ..color = color.withAlpha(204); // 0.8 opacity (204/255)
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CapsulePainter old) => old.color != color;
}