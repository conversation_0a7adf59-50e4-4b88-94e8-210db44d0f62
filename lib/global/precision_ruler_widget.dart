import 'package:flutter/material.dart';
import 'dart:math';
import 'package:flutter/services.dart';

/// A precision ruler widget that mimics a vintage camera-style horizontal slider.
///
/// This widget provides a customizable ruler with major and minor tick marks,
/// and supports both fixed ruler with moving pointer and moving ruler with fixed pointer modes.
/// It automatically adapts to smaller spaces by scaling down elements appropriately.
///
/// Features:
/// - Customizable tick marks, colors, and dimensions
/// - Support for both fixed and moving ruler modes
/// - Automatic scaling based on available space
/// - Snapping to tick marks with animation
/// - RTL support
/// - Optional widgets at the start and end of the ruler
class PrecisionRuler extends StatefulWidget {
  /// Current value of the ruler.
  final double value;

  /// Height of the ruler widget.
  final double height;

  /// Optional width of the ruler widget. If not provided, calculated based on height.
  final double? width;

  /// Background color of the ruler.
  final Color backgroundColor;

  /// Color of the tick marks.
  final Color tickColor;

  /// Border color for tick marks. If null, no border is drawn.
  final Color? tickBorderColor;

  /// Border width for tick marks.
  final double tickBorderWidth;

  /// Border radius for tick marks. If null, half of the tick width is used.
  final double? tickBorderRadius;

  /// Width of major tick marks.
  final double majorTickWidth;

  /// Height of major tick marks as a percentage of the ruler height (0.0 to 1.0).
  final double majorTickHeightRatio;

  /// Width of medium tick marks.
  final double mediumTickWidth;

  /// Height of medium tick marks as a percentage of the ruler height (0.0 to 1.0).
  final double mediumTickHeightRatio;

  /// Width of minor tick marks.
  final double minorTickWidth;

  /// Height of minor tick marks as a percentage of the ruler height (0.0 to 1.0).
  final double minorTickHeightRatio;

  /// Color of the pointer marker.
  final Color pointerColor;

  /// Opacity of the background (0.0 to 1.0).
  final double backgroundOpacity;

  /// Radius of the glow effect around the ruler.
  final double glowRadius;

  /// Color of the glow effect.
  final Color glowColor;

  /// Number of ticks visible in the ruler viewport.
  final int visibleTicks;

  /// Number of minor ticks between major ticks.
  final int minorTicksPerMajor;

  /// Interval between major ticks (e.g., 5 for 0,5,10,15 or 2 for 0,2,4,6).
  final int majorTickInterval;

  /// Interval between medium ticks (e.g., 1 for medium ticks at every integer, 2 for medium ticks at even numbers).
  /// If null, medium ticks will be determined automatically (even numbers that are not major ticks).
  final int? mediumTickInterval;

  /// Width percentage of the ruler within the container (0.0 to 1.0).
  /// A value of 0.9 means the ruler takes 90% of the container width.
  final double rulerWidthFactor;

  /// Whether to force the ruler to take the full width regardless of the value range.
  /// When true, the ruler will always span the full width, even with large value ranges.
  final bool forceFullWidth;

  /// The reference range to use when forceFullWidth is true.
  /// This determines how compressed or expanded the ticks appear.
  /// A smaller value makes ticks more spread out, a larger value makes them more compressed.
  /// Default is 20.0 (equivalent to a range from -10 to 10).
  final double referenceRange;

  /// Whether to show a baseline (horizontal line) at the bottom of the ruler.
  final bool showBaseline;

  /// Color of the baseline. If not provided, uses the tick color.
  final Color? baselineColor;

  /// Thickness of the baseline.
  final double baselineThickness;

  /// Corner radius of the ruler container.
  final double cornerRadius;

  /// Whether to move the pointer (true) or the ruler (false).
  final bool movePointer;

  /// Width of the pointer marker.
  final double pointerWidth;

  /// Height of the pointer marker.
  final double pointerHeight;

  /// Spacing between the top of the container and the pointer.
  final double pointerTopSpacing;

  /// Vertical padding inside the ruler container.
  final double rulerPadding;

  /// Horizontal padding inside the ruler container.
  final double horizontalPadding;

  /// Whether to enable snapping to the nearest tick.
  final bool enableSnapping;

  /// Minimum value of the ruler.
  final double minValue;

  /// Maximum value of the ruler.
  final double maxValue;

  /// Callback when the value changes.
  final ValueChanged<double>? onChanged;

  /// Interval for snapping (e.g., 0.5 for half-ticks, 0.25 for quarter-ticks).
  final double snapInterval;

  /// Whether to show numeric labels on major ticks.
  final bool showLabels;

  /// Text style for the numeric labels.
  final TextStyle? labelStyle;

  /// Function to format the label text. If null, the tick value is displayed as is.
  final String Function(int value)? labelFormatter;

  /// Duration of the snap animation.
  final Duration snapAnimationDuration;

  /// Whether to automatically adapt to smaller spaces by hiding labels and simplifying the ruler.
  final bool autoAdaptToSpace;

  /// Whether to automatically hide labels when the ruler is scaled below a certain width.
  final bool autoHideLabels;

  /// The width threshold below which labels are automatically hidden if autoHideLabels is true.
  final double labelHideThreshold;

  /// The width threshold below which minor ticks are hidden for better readability in small spaces.
  final double minorTickHideThreshold;

  /// Whether to show only major ticks with alternating sizes (one large, one medium).
  /// When true, minor ticks are not shown regardless of minorTicksPerMajor value.
  final bool showAlternatingMajorTicks;

  /// Optional widget to display at the start (left side) of the ruler.
  /// This will be positioned at the left edge of the ruler container.
  final Widget? startWidget;

  /// Optional widget to display at the end (right side) of the ruler.
  /// This will be positioned at the right edge of the ruler container.
  final Widget? endWidget;

  const PrecisionRuler({
    super.key,
    required this.value,
    this.height = 40,
    this.width,
    this.backgroundColor = Colors.black,
    this.tickColor = Colors.white,
    this.tickBorderColor,
    this.tickBorderWidth = 0.5,
    this.tickBorderRadius,
    this.majorTickWidth = 2.0,
    this.majorTickHeightRatio = 0.4,
    this.mediumTickWidth = 1.5,
    this.mediumTickHeightRatio = 0.25,
    this.minorTickWidth = 1.0,
    this.minorTickHeightRatio = 0.15,
    this.pointerColor = Colors.white,
    this.backgroundOpacity = 0.7,
    this.glowRadius = 8.0,
    this.glowColor = Colors.white,
    this.visibleTicks = 21,
    this.minorTicksPerMajor = 4,
    this.majorTickInterval = 5,
    this.mediumTickInterval,
    this.rulerWidthFactor = 1.0, // Default to 100% width
    this.forceFullWidth = false, // Default to false
    this.referenceRange = 20.0, // Default to 20.0 (range from -10 to 10)
    this.showBaseline = true, // Default to showing the baseline
    this.baselineColor, // Default to null (will use tick color)
    this.baselineThickness = 2.1, // Default thickness
    this.cornerRadius = 4.0,
    this.movePointer = false,
    this.pointerWidth = 10,
    this.pointerHeight = 10,
    this.pointerTopSpacing = 4.0,
    this.rulerPadding = 4.0,
    this.horizontalPadding = 15.0,
    this.enableSnapping = true,
    this.minValue = -10.0,
    this.maxValue = 10.0,
    this.onChanged,
    this.snapInterval = 1.0,
    this.showLabels = false,
    this.labelStyle,
    this.labelFormatter,
    this.snapAnimationDuration = const Duration(milliseconds: 200),
    this.autoAdaptToSpace = true,
    this.autoHideLabels = true,
    this.labelHideThreshold = 200.0,
    this.minorTickHideThreshold = 100.0,
    this.showAlternatingMajorTicks = false,
    this.startWidget,
    this.endWidget,
  })  : assert(snapInterval > 0, 'Snap interval must be greater than 0'),
        assert(rulerWidthFactor > 0 && rulerWidthFactor <= 1.0,
            'Ruler width factor must be between 0 and 1.0');

  @override
  State<PrecisionRuler> createState() => _PrecisionRulerState();
}

class _PrecisionRulerState extends State<PrecisionRuler>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;
  late double _tickSpacing;
  late double _startValue;
  late double _endValue;

  @override
  void initState() {
    super.initState();

    // Initialize animation controller for snapping
    _animationController = AnimationController(
      vsync: this,
      duration: widget.snapAnimationDuration,
    );

    _animation = Tween<double>(begin: widget.value, end: widget.value).animate(
        CurvedAnimation(
            parent: _animationController, curve: Curves.easeOutCubic))
      ..addListener(() {
        if (widget.onChanged != null) {
          widget.onChanged!(_animation.value);
        }
      });

    // Initial tick spacing will be calculated in build method
    _tickSpacing = 0;
  }

  @override
  void didUpdateWidget(PrecisionRuler oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Update animation duration if it changes
    if (oldWidget.snapAnimationDuration != widget.snapAnimationDuration) {
      _animationController.duration = widget.snapAnimationDuration;
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final containerHeight = widget.height;
    final isRtl = Directionality.of(context) == TextDirection.rtl;

    // Use LayoutBuilder to get the available width from the parent container
    return LayoutBuilder(
      builder: (context, constraints) {
        // Use the available width from the parent container if width is not provided
        final containerWidth = widget.width ?? constraints.maxWidth;

        // Determine size category based on available width for auto-adaptation
        final bool isVerySmall = containerWidth < widget.minorTickHideThreshold;
        final bool isSmall =
            containerWidth < widget.labelHideThreshold && !isVerySmall;

        // Calculate effective parameters based on available space
        final effectiveShowLabels = widget.autoHideLabels &&
                widget.autoAdaptToSpace &&
                (isSmall || isVerySmall)
            ? false
            : widget.showLabels;

        // Adjust minor ticks for small spaces if auto-adaptation is enabled
        final effectiveMinorTicksPerMajor =
            widget.autoAdaptToSpace && isVerySmall
                ? 0
                : widget.minorTicksPerMajor;

        // Calculate a scaling factor based on container width
        double scaleFactor;

        // Simplified scaling factor calculation
        if (widget.autoAdaptToSpace) {
          if (isVerySmall) {
            scaleFactor = 0.5; // Very small scale for tiny containers
          } else if (isSmall) {
            scaleFactor = 0.7; // Smaller scale for small containers
          } else if (containerWidth < 400) {
            scaleFactor = 0.9; // Slightly reduced scale for medium containers
          } else if (containerWidth > 800) {
            scaleFactor = 1.2; // Larger scale for very large containers
          } else {
            scaleFactor = 1.0; // Default scale
          }
        } else {
          // Map container width to scale factor using a more continuous approach
          if (containerWidth <= 200) {
            scaleFactor = 0.7;
          } else if (containerWidth <= 300) {
            scaleFactor = 0.8;
          } else if (containerWidth <= 400) {
            scaleFactor = 0.9;
          } else if (containerWidth >= 800) {
            scaleFactor = 1.2;
          } else {
            scaleFactor = 1.0;
          }
        }

        // Calculate the effective horizontal padding
        final effectiveHorizontalPadding =
            widget.horizontalPadding * scaleFactor;

        // Calculate the available width after padding
        final availableWidth =
            containerWidth - (effectiveHorizontalPadding * 2);

        // Recalculate tick spacing based on the available width and the range of values
        final valueRange = widget.maxValue - widget.minValue;
        if (valueRange > 0) {
          // Calculate the spacing to distribute ticks evenly across the available width
          if (widget.forceFullWidth) {
            // When forceFullWidth is true, we use the specified reference range
            // This ensures consistent spacing regardless of the actual value range
            _tickSpacing = availableWidth / widget.referenceRange;
          } else {
            // Use the exact value range to ensure the ruler spans the entire available width
            _tickSpacing = availableWidth / valueRange;
          }

          // Ensure minimum spacing between ticks for readability
          _tickSpacing = _tickSpacing.clamp(5.0 * scaleFactor, double.infinity);
        }

        // Calculate scaled dimensions for the pointer and other elements
        final effectivePointerWidth = widget.pointerWidth * scaleFactor;
        final effectivePointerHeight = widget.pointerHeight * scaleFactor;
        final effectivePointerTopSpacing =
            widget.pointerTopSpacing * scaleFactor;
        final effectiveRulerPadding = widget.rulerPadding * scaleFactor;
        final effectiveCornerRadius = widget.cornerRadius * scaleFactor;
        final effectiveGlowRadius = widget.glowRadius * scaleFactor;
        final effectiveBaselineThickness =
            widget.baselineThickness * scaleFactor;

        // Adjust text style for labels based on available space
        final effectiveLabelStyle = isSmall || isVerySmall
            ? (widget.labelStyle?.copyWith(fontSize: isVerySmall ? 6 : 8) ??
                TextStyle(
                    fontSize: isVerySmall ? 6 : 8, color: widget.tickColor))
            : widget.labelStyle;

        return SizedBox(
          height: containerHeight,
          width: containerWidth,
          child: Stack(
            clipBehavior: Clip.none,
            children: [
              // Background with glow
              //! BUG: fix start widget should not be stack to avoid ui overflow
              Container(
                height: containerHeight,
                width: containerWidth,
                decoration: BoxDecoration(
                    //gradient top white to bottom black
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.white.withAlpha(128), // 0.50 opacity
                        Colors.black.withAlpha(128), // 0.50 opacity
                        Colors.black.withAlpha(204), // 0.80 opacity
                      ],
                      stops: const [0.0, 0.3, 1.0],
                    ),
                    borderRadius: BorderRadius.circular(effectiveCornerRadius),
                    boxShadow: [
                      BoxShadow(
                        color: widget.glowColor.withAlpha(76), // 0.3 opacity
                        blurRadius: effectiveGlowRadius,
                        spreadRadius: 1,
                      ),
                    ],
                    border: Border.all(
                      color: Colors.white,
                      width: 0.8,
                    )),
              ),

              if (widget.movePointer)
                // Fixed ruler with moving pointer
                _buildMovingPointerRuler(
                  containerHeight,
                  containerWidth,
                  effectiveMinorTicksPerMajor,
                  effectiveShowLabels,
                  effectiveLabelStyle,
                  effectivePointerWidth,
                  effectivePointerHeight,
                  effectivePointerTopSpacing,
                  effectiveRulerPadding,
                  effectiveBaselineThickness,
                  scaleFactor,
                )
              else
                // Moving ruler with fixed pointer
                _buildMovingRulerFixedPointer(
                  containerHeight,
                  containerWidth,
                  effectiveMinorTicksPerMajor,
                  effectiveShowLabels,
                  effectiveLabelStyle,
                  effectivePointerWidth,
                  effectivePointerHeight,
                  effectivePointerTopSpacing,
                  effectiveRulerPadding,
                  effectiveBaselineThickness,
                  scaleFactor,
                ),

              // Start widget (positioned at the left or right depending on RTL)
              if (widget.startWidget != null)
                Positioned(
                  left: isRtl ? null : effectiveHorizontalPadding,
                  right: isRtl ? effectiveHorizontalPadding : null,
                  top: 0,
                  bottom: 0,
                  child: Center(
                    child: widget.startWidget!,
                  ),
                ),

              // End widget (positioned at the right or left depending on RTL)
              if (widget.endWidget != null)
                Positioned(
                  right: isRtl ? null : effectiveHorizontalPadding,
                  left: isRtl ? effectiveHorizontalPadding : null,
                  top: 0,
                  bottom: 0,
                  child: Center(
                    child: widget.endWidget!,
                  ),
                ),

              // Gesture detector for interaction
              if (widget.onChanged != null)
                Positioned(
                  // Position the gesture detector to cover the ruler area
                  // but leave space for the start and end widgets if they exist
                  left: widget.startWidget != null
                      ? effectiveHorizontalPadding * 3
                      : 0,
                  right: widget.endWidget != null
                      ? effectiveHorizontalPadding * 3
                      : 0,
                  top: 0,
                  bottom: 0,
                  child: GestureDetector(
                    behavior: HitTestBehavior.opaque,
                    onHorizontalDragUpdate: (details) {
                      // Apply RTL adjustment if needed
                      final adjustedDelta =
                          isRtl ? -details.delta.dx : details.delta.dx;
                      final delta = adjustedDelta / _tickSpacing;

                      // Calculate new value based on mode
                      final newValue = widget.movePointer
                          ? widget.value + delta
                          : widget.value - delta;

                      // Apply the change
                      widget.onChanged!(
                          newValue.clamp(widget.minValue, widget.maxValue));
                    },
                    onHorizontalDragEnd: widget.enableSnapping
                        ? (_) {
                            // Snap to nearest tick when drag ends
                            _snapToNearestTick();
                          }
                        : null,
                    // Make the gesture detector fill the available space
                    child: Container(
                      color: Colors.transparent,
                    ),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }

  // Snap to the nearest tick with animation
  void _snapToNearestTick() {
    if (widget.onChanged == null) return;

    // Calculate the nearest tick value based on snap interval
    final nearestValue =
        (widget.value / widget.snapInterval).round() * widget.snapInterval;

    // Ensure the value stays within the min/max range
    final clampedValue = nearestValue.clamp(widget.minValue, widget.maxValue);

    // Only snap if we're not already at a tick
    if ((widget.value - clampedValue).abs() > 0.01) {
      // Set up animation
      _startValue = widget.value;
      _endValue = clampedValue;

      _animation = Tween<double>(begin: _startValue, end: _endValue).animate(
          CurvedAnimation(
              parent: _animationController, curve: Curves.easeOutCubic));

      // Reset and start animation
      _animationController.reset();
      _animationController.forward();

      // Provide haptic feedback on major ticks
      if (clampedValue % widget.majorTickInterval == 0) {
        HapticFeedback.lightImpact();
      }
    }
  }

  Widget _buildMovingRulerFixedPointer(
    double containerHeight,
    double containerWidth,
    int effectiveMinorTicksPerMajor,
    bool effectiveShowLabels,
    TextStyle? effectiveLabelStyle,
    double effectivePointerWidth,
    double effectivePointerHeight,
    double effectivePointerTopSpacing,
    double effectiveRulerPadding,
    double effectiveBaselineThickness,
    double scaleFactor,
  ) {
    // Clamp the value to ensure the ruler stops at min/max
    final clampedValue = widget.value.clamp(widget.minValue, widget.maxValue);

    // Calculate the effective horizontal padding
    final effectiveHorizontalPadding = widget.horizontalPadding * scaleFactor;

    // Calculate the available width after padding
    final availableWidth = containerWidth - (effectiveHorizontalPadding * 2);

    return Stack(
      children: [
        // Ticks that move - full width with padding
        SizedBox(
          width: containerWidth,
          height: containerHeight,
          child: Padding(
            padding: EdgeInsets.only(
              top: effectiveRulerPadding,
              bottom: effectiveRulerPadding,
              left: effectiveHorizontalPadding,
              right: effectiveHorizontalPadding,
            ),
            child: RulerWidget(
              value: clampedValue,
              tickColor: widget.tickColor,
              tickBorderColor: widget.tickBorderColor,
              tickBorderWidth: widget.tickBorderWidth,
              tickBorderRadius: widget.tickBorderRadius,
              majorTickWidth: widget.majorTickWidth,
              majorTickHeightRatio: widget.majorTickHeightRatio,
              mediumTickWidth: widget.mediumTickWidth,
              mediumTickHeightRatio: widget.mediumTickHeightRatio,
              minorTickWidth: widget.minorTickWidth,
              minorTickHeightRatio: widget.minorTickHeightRatio,
              visibleTicks: widget.visibleTicks,
              minorTicksPerMajor: effectiveMinorTicksPerMajor,
              majorTickInterval: widget.majorTickInterval,
              mediumTickInterval: widget.mediumTickInterval,
              minValue: widget.minValue,
              maxValue: widget.maxValue,
              showLabels: effectiveShowLabels,
              labelStyle: effectiveLabelStyle,
              tickSpacing: _tickSpacing,
              scaleFactor: scaleFactor,
              labelFormatter: widget.labelFormatter,
              rulerWidthFactor: widget.rulerWidthFactor,
              forceFullWidth: widget.forceFullWidth,
              referenceRange: widget.referenceRange,
              showBaseline: widget.showBaseline,
              baselineColor: widget.baselineColor,
              baselineThickness: effectiveBaselineThickness,
              showAlternatingMajorTicks: widget.showAlternatingMajorTicks,
              size: Size(availableWidth,
                  containerHeight - (effectiveRulerPadding * 2)),
            ),
          ),
        ),

        // Fixed pointer in center - scale based on container width
        Positioned(
          top: effectivePointerTopSpacing,
          left: 0,
          right: 0,
          child: Center(
            child: CustomPaint(
              size: Size(effectivePointerWidth, effectivePointerHeight),
              painter: PointerMarkerPainter(
                color: widget.pointerColor,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildMovingPointerRuler(
    double containerHeight,
    double containerWidth,
    int effectiveMinorTicksPerMajor,
    bool effectiveShowLabels,
    TextStyle? effectiveLabelStyle,
    double effectivePointerWidth,
    double effectivePointerHeight,
    double effectivePointerTopSpacing,
    double effectiveRulerPadding,
    double effectiveBaselineThickness,
    double scaleFactor,
  ) {
    // Clamp the value to ensure it stays within bounds
    final clampedValue = widget.value.clamp(widget.minValue, widget.maxValue);

    // Calculate the value range
    final valueRange = widget.maxValue - widget.minValue;

    // Calculate the effective horizontal padding
    final effectiveHorizontalPadding = widget.horizontalPadding * scaleFactor;

    // Calculate the actual width of the ruler with padding
    final availableWidth = containerWidth - (effectiveHorizontalPadding * 2);
    final rulerWidth = availableWidth * widget.rulerWidthFactor;

    // Calculate the tick spacing to ensure the ruler spans the specified width percentage
    double adjustedTickSpacing;
    if (widget.forceFullWidth) {
      // When forceFullWidth is true, we use the specified reference range
      // This ensures consistent spacing regardless of the actual value range
      adjustedTickSpacing = rulerWidth / widget.referenceRange;
    } else {
      adjustedTickSpacing = rulerWidth / valueRange;
    }

    // Calculate the starting X position to center the ruler
    final startX =
        effectiveHorizontalPadding + (availableWidth - rulerWidth) / 2;

    // Calculate the exact position for min value
    final minValueX = startX;

    // Calculate pointer position based on value and ruler width factor
    // Map the value from [minValue, maxValue] to [minValueX, maxValueX]
    final valueRatio = (clampedValue - widget.minValue) / valueRange;
    final adjustedPointerX = minValueX + (valueRatio * rulerWidth);

    return Stack(
      clipBehavior: Clip.none,
      children: [
        // Fixed ruler - full width with padding
        SizedBox(
          width: containerWidth,
          height: containerHeight,
          child: Padding(
            padding: EdgeInsets.only(
              top: effectiveRulerPadding,
              bottom: effectiveRulerPadding,
              left: effectiveHorizontalPadding,
              right: effectiveHorizontalPadding,
            ),
            child: FixedRulerWidget(
              tickColor: widget.tickColor,
              tickBorderColor: widget.tickBorderColor,
              tickBorderWidth: widget.tickBorderWidth,
              tickBorderRadius: widget.tickBorderRadius,
              majorTickWidth: widget.majorTickWidth,
              majorTickHeightRatio: widget.majorTickHeightRatio,
              mediumTickWidth: widget.mediumTickWidth,
              mediumTickHeightRatio: widget.mediumTickHeightRatio,
              minorTickWidth: widget.minorTickWidth,
              minorTickHeightRatio: widget.minorTickHeightRatio,
              visibleTicks: widget.visibleTicks,
              minorTicksPerMajor: effectiveMinorTicksPerMajor,
              majorTickInterval: widget.majorTickInterval,
              mediumTickInterval: widget.mediumTickInterval,
              minValue: widget.minValue,
              maxValue: widget.maxValue,
              showLabels: effectiveShowLabels,
              labelStyle: effectiveLabelStyle,
              tickSpacing: adjustedTickSpacing,
              scaleFactor: scaleFactor,
              labelFormatter: widget.labelFormatter,
              rulerWidthFactor: widget.rulerWidthFactor,
              forceFullWidth: widget.forceFullWidth,
              referenceRange: widget.referenceRange,
              showBaseline: widget.showBaseline,
              baselineColor: widget.baselineColor,
              baselineThickness: effectiveBaselineThickness,
              showAlternatingMajorTicks: widget.showAlternatingMajorTicks,
              size: Size(availableWidth,
                  containerHeight - (effectiveRulerPadding * 2)),
            ),
          ),
        ),

        // Moving pointer - scale based on container width
        Positioned(
          left: adjustedPointerX - (effectivePointerWidth / 2),
          top: effectivePointerTopSpacing,
          child: CustomPaint(
            size: Size(effectivePointerWidth, effectivePointerHeight),
            painter: PointerMarkerPainter(
              color: widget.pointerColor,
            ),
          ),
        ),
      ],
    );
  }
}

/// Widget for drawing the ruler with moving ticks and fixed pointer
class RulerWidget extends StatelessWidget {
  final double value;
  final Color tickColor;
  final Color? tickBorderColor;
  final double tickBorderWidth;
  final double? tickBorderRadius;
  final double majorTickWidth;
  final double majorTickHeightRatio;
  final double mediumTickWidth;
  final double mediumTickHeightRatio;
  final double minorTickWidth;
  final double minorTickHeightRatio;
  final int visibleTicks;
  final int minorTicksPerMajor;
  final int majorTickInterval;
  final int? mediumTickInterval;
  final double minValue;
  final double maxValue;
  final bool showLabels;
  final TextStyle? labelStyle;
  final double tickSpacing;
  final double scaleFactor;
  final String Function(int value)? labelFormatter;
  final double rulerWidthFactor;
  final bool forceFullWidth;
  final double referenceRange;
  final bool showBaseline;
  final Color? baselineColor;
  final double baselineThickness;
  final bool showAlternatingMajorTicks;
  final Size size;

  const RulerWidget({
    super.key,
    required this.value,
    required this.tickColor,
    this.tickBorderColor,
    this.tickBorderWidth = 0.5,
    this.tickBorderRadius,
    this.majorTickWidth = 2.0,
    this.majorTickHeightRatio = 0.4,
    this.mediumTickWidth = 1.5,
    this.mediumTickHeightRatio = 0.25,
    this.minorTickWidth = 1.0,
    this.minorTickHeightRatio = 0.15,
    required this.visibleTicks,
    this.minorTicksPerMajor = 4,
    this.majorTickInterval = 5,
    this.mediumTickInterval,
    this.minValue = -10.0,
    this.maxValue = 10.0,
    this.showLabels = true,
    this.labelStyle,
    required this.tickSpacing,
    this.scaleFactor = 1.0,
    this.labelFormatter,
    this.rulerWidthFactor = 1.0,
    this.forceFullWidth = false,
    this.referenceRange = 20.0,
    this.showBaseline = true,
    this.baselineColor,
    this.baselineThickness = 1.0,
    this.showAlternatingMajorTicks = false,
    required this.size,
  });

  @override
  Widget build(BuildContext context) {
    final height = size.height;

    // Apply scale factor to tick heights using the ratio parameters
    final longTickHeight = height * majorTickHeightRatio * scaleFactor;
    final mediumTickHeight = height * mediumTickHeightRatio * scaleFactor;
    final shortTickHeight = height * minorTickHeightRatio * scaleFactor;
    final baselineY = height * 0.9;

    // Calculate the actual width of the ruler based on the width factor
    final rulerWidth = size.width * rulerWidthFactor;

    // Calculate the tick spacing to ensure the ruler spans the specified width percentage
    // For moving ruler, we need to adjust based on the current value
    double adjustedTickSpacing;
    if (forceFullWidth) {
      // When forceFullWidth is true, we use the specified reference range
      // This ensures consistent spacing regardless of the actual value range
      adjustedTickSpacing = rulerWidth / referenceRange;
    } else {
      adjustedTickSpacing = rulerWidth / (maxValue - minValue);
    }

    // Apply scale factor to text size
    final textStyle = labelStyle ??
        TextStyle(
          fontSize: height * 0.25 * scaleFactor,
          color: tickColor,
          fontWeight: FontWeight.bold,
        );

    // Calculate visible range based on current value
    final half = visibleTicks ~/ 2;
    final startTick = value.toInt() - half;
    final endTick = value.toInt() + half;

    // Calculate the position of the minimum value on the ruler
    final valueRange = maxValue - minValue;
    final valueRatio = (value - minValue) / valueRange;
    final centerX = size.width / 2;
    final totalRulerWidth = valueRange * adjustedTickSpacing;
    final minValueX = centerX - (valueRatio * totalRulerWidth);

    // Calculate visible tick range based on screen coordinates
    final minVisibleValue = max(minValue, startTick.toDouble());
    final maxVisibleValue = min(maxValue, endTick.toDouble());

    // Pre-calculate tick properties for better performance
    final majorTickStrokeWidth = majorTickWidth * scaleFactor;
    final mediumTickStrokeWidth = mediumTickWidth * scaleFactor;
    final minorTickStrokeWidth = minorTickWidth * scaleFactor;

    // Generate tick widgets
    List<Widget> generateTicks() {
      final ticks = <Widget>[];

      // Add baseline if enabled
      // Add baseline if enabled
      if (showBaseline) {
        final baselineColor = this.baselineColor ?? tickColor;
        final baselineThickness = this.baselineThickness * scaleFactor;

        // Calculate the visible range of values
        final valueRange = maxValue - minValue;
        final valueRatio = (value - minValue) / valueRange;
        final centerX = size.width / 2;
        final totalRulerWidth = valueRange * adjustedTickSpacing;
        final minValueX = centerX - (valueRatio * totalRulerWidth);

        // Get the maximum tick width to ensure baseline covers all tick types
        final maxTickWidth = max(majorTickStrokeWidth,
            max(mediumTickStrokeWidth, minorTickStrokeWidth));

        final baselineStartX = max(0.0, minValueX) - maxTickWidth / 2;
        final baselineEndX =
            min(size.width, minValueX + totalRulerWidth) + maxTickWidth / 2;

        // Add baseline as a positioned container
        ticks.add(
          Positioned(
            left: baselineStartX,
            top: baselineY - (baselineThickness / 2),
            child: Container(
              width: baselineEndX - baselineStartX,
              height: baselineThickness,
              color: baselineColor,
            ),
          ),
        );
      }

      // Draw ticks within the visible range
      for (int i = minVisibleValue.floor(); i <= maxVisibleValue.ceil(); i++) {
        // Skip values outside our range
        if (i < minValue || i > maxValue) continue;

        final tickValue = i.toDouble();

        // Calculate x position
        final x = minValueX + ((tickValue - minValue) * adjustedTickSpacing);

        // Skip ticks outside the visible area (optimization)
        if (x < -10 || x > size.width + 10) continue;

        // Determine tick type
        bool isLongTick;
        bool isMediumTick;

        if (showAlternatingMajorTicks) {
          // Alternating major ticks mode
          final isMajorTick = i % majorTickInterval == 0;
          if (!isMajorTick) continue; // Skip non-major ticks

          // Alternate between long and medium ticks
          final majorTickIndex = i ~/ majorTickInterval;
          isLongTick = majorTickIndex % 2 == 0;
          isMediumTick = !isLongTick;
        } else {
          // Standard mode
          isLongTick = i % majorTickInterval == 0;

          // Use mediumTickInterval if provided, otherwise default to even numbers
          final effectiveMediumInterval = mediumTickInterval ?? 2;
          isMediumTick = !isLongTick && i % effectiveMediumInterval == 0;
        }

        // Set tick width based on tick type
        final tickWidth = isLongTick
            ? majorTickStrokeWidth
            : (isMediumTick ? mediumTickStrokeWidth : minorTickStrokeWidth);

        // Determine tick height
        final tickHeight = isLongTick
            ? longTickHeight
            : (isMediumTick ? mediumTickHeight : shortTickHeight);

        // Create tick container
        ticks.add(
          Positioned(
            left: x - (tickWidth / 2), // Center the tick at the x position
            top: baselineY - tickHeight, // Position from baseline
            child: Container(
              width: tickWidth,
              height: tickHeight,
              decoration: BoxDecoration(
                color: tickColor,
                borderRadius: BorderRadius.circular(
                  tickBorderRadius ?? (tickWidth / 2),
                ),
                border: tickBorderColor != null
                    ? Border.all(
                        color: tickBorderColor!,
                        width: tickBorderWidth,
                      )
                    : null,
              ),
            ),
          ),
        );

        // Add label for major ticks
        if (showLabels && isLongTick) {
          final labelText =
              labelFormatter != null ? labelFormatter!(i) : i.toString();
          ticks.add(
            Positioned(
              left: x,
              top: baselineY + 5,
              child: Text(
                labelText,
                style: textStyle,
                textAlign: TextAlign.center,
              ),
            ),
          );
        }

        // Add minor ticks between major ticks
        if (isLongTick &&
            i < maxVisibleValue &&
            !showAlternatingMajorTicks &&
            minorTicksPerMajor > 0) {
          final minorTickSpacing =
              adjustedTickSpacing / (minorTicksPerMajor + 1);

          for (int j = 1; j <= minorTicksPerMajor; j++) {
            final minorTickValue = i + j / (minorTicksPerMajor + 1);

            // Skip if outside boundaries
            if (minorTickValue < minValue || minorTickValue > maxValue)
              continue;

            // Calculate minor tick position
            final minorX = x + (j * minorTickSpacing);

            // Skip if outside visible area
            if (minorX < 0 || minorX > size.width) continue;

            // Determine if this is a medium minor tick (middle one)
            final isMinorMedium = j == (minorTicksPerMajor + 1) ~/ 2;

            // Set tick width
            final minorTickWidth =
                isMinorMedium ? mediumTickStrokeWidth : minorTickStrokeWidth;

            // Determine height
            final minorTickHeight =
                isMinorMedium ? mediumTickHeight : shortTickHeight;

            // Create minor tick container
            ticks.add(
              Positioned(
                left: minorX -
                    (minorTickWidth / 2), // Center the tick at the x position
                top: baselineY - minorTickHeight, // Position from baseline
                child: Container(
                  width: minorTickWidth,
                  height: minorTickHeight,
                  decoration: BoxDecoration(
                    color: tickColor,
                    borderRadius: BorderRadius.circular(
                      tickBorderRadius ?? (minorTickWidth / 2),
                    ),
                    border: tickBorderColor != null
                        ? Border.all(
                            color: tickBorderColor!,
                            width: tickBorderWidth,
                          )
                        : null,
                  ),
                ),
              ),
            );
          }
        }
      }

      return ticks;
    }

    return Stack(
      clipBehavior: Clip.none,
      children: generateTicks(),
    );
  }
}

/// Custom painter for drawing the ruler with moving ticks and fixed pointer
/// This is kept for backward compatibility but delegates to RulerWidget
class RulerPainter extends CustomPainter {
  final double value;
  final Color tickColor;
  final Color? tickBorderColor;
  final double tickBorderWidth;
  final double? tickBorderRadius;
  final double majorTickWidth;
  final double majorTickHeightRatio;
  final double mediumTickWidth;
  final double mediumTickHeightRatio;
  final double minorTickWidth;
  final double minorTickHeightRatio;
  final int visibleTicks;
  final int minorTicksPerMajor;
  final int majorTickInterval;
  final int? mediumTickInterval;
  final double minValue;
  final double maxValue;
  final bool showLabels;
  final TextStyle? labelStyle;
  final double tickSpacing;
  final double scaleFactor;
  final String Function(int value)? labelFormatter;
  final double rulerWidthFactor;

  final bool forceFullWidth;
  final double referenceRange;
  final bool showBaseline;
  final Color? baselineColor;
  final double baselineThickness;
  final bool showAlternatingMajorTicks;

  RulerPainter({
    required this.value,
    required this.tickColor,
    this.tickBorderColor,
    this.tickBorderWidth = 0.5,
    this.tickBorderRadius,
    this.majorTickWidth = 2.0,
    this.majorTickHeightRatio = 0.4,
    this.mediumTickWidth = 1.5,
    this.mediumTickHeightRatio = 0.25,
    this.minorTickWidth = 1.0,
    this.minorTickHeightRatio = 0.15,
    required this.visibleTicks,
    this.minorTicksPerMajor = 4,
    this.majorTickInterval = 5,
    this.mediumTickInterval,
    this.minValue = -10.0,
    this.maxValue = 10.0,
    this.showLabels = true,
    this.labelStyle,
    required this.tickSpacing,
    this.scaleFactor = 1.0,
    this.labelFormatter,
    this.rulerWidthFactor = 1.0,
    this.forceFullWidth = false,
    this.referenceRange = 20.0,
    this.showBaseline = true,
    this.baselineColor,
    this.baselineThickness = 1.0,
    this.showAlternatingMajorTicks = false,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // This is now just a stub that renders nothing
    // The actual rendering is done by RulerWidget
  }

  @override
  bool shouldRepaint(RulerPainter oldDelegate) {
    // Only check properties that would visually affect the painting
    return oldDelegate.value != value ||
        oldDelegate.tickSpacing != tickSpacing ||
        oldDelegate.tickColor != tickColor ||
        oldDelegate.tickBorderColor != tickBorderColor ||
        oldDelegate.tickBorderWidth != tickBorderWidth ||
        oldDelegate.tickBorderRadius != tickBorderRadius ||
        oldDelegate.majorTickWidth != majorTickWidth ||
        oldDelegate.majorTickHeightRatio != majorTickHeightRatio ||
        oldDelegate.mediumTickWidth != mediumTickWidth ||
        oldDelegate.mediumTickHeightRatio != mediumTickHeightRatio ||
        oldDelegate.minorTickWidth != minorTickWidth ||
        oldDelegate.minorTickHeightRatio != minorTickHeightRatio ||
        oldDelegate.showLabels != showLabels ||
        oldDelegate.showBaseline != showBaseline ||
        oldDelegate.showAlternatingMajorTicks != showAlternatingMajorTicks ||
        // Check dimension-related properties
        oldDelegate.minorTicksPerMajor != minorTicksPerMajor ||
        oldDelegate.majorTickInterval != majorTickInterval ||
        oldDelegate.mediumTickInterval != mediumTickInterval ||
        oldDelegate.minValue != minValue ||
        oldDelegate.maxValue != maxValue ||
        oldDelegate.scaleFactor != scaleFactor ||
        oldDelegate.rulerWidthFactor != rulerWidthFactor ||
        oldDelegate.forceFullWidth != forceFullWidth ||
        oldDelegate.referenceRange != referenceRange ||
        // Check style-related properties
        oldDelegate.baselineColor != baselineColor ||
        oldDelegate.baselineThickness != baselineThickness;
  }
}

/// Widget for drawing the fixed ruler with moving pointer
class FixedRulerWidget extends StatelessWidget {
  final Color tickColor;
  final Color? tickBorderColor;
  final double tickBorderWidth;
  final double? tickBorderRadius;
  final double majorTickWidth;
  final double majorTickHeightRatio;
  final double mediumTickWidth;
  final double mediumTickHeightRatio;
  final double minorTickWidth;
  final double minorTickHeightRatio;
  final int visibleTicks;
  final int minorTicksPerMajor;
  final int majorTickInterval;
  final int? mediumTickInterval;
  final double minValue;
  final double maxValue;
  final bool showLabels;
  final TextStyle? labelStyle;
  final double tickSpacing;
  final double scaleFactor;
  final String Function(int value)? labelFormatter;
  final double rulerWidthFactor;
  final bool forceFullWidth;
  final double referenceRange;
  final bool showBaseline;
  final Color? baselineColor;
  final double baselineThickness;
  final bool showAlternatingMajorTicks;
  final Size size;

  const FixedRulerWidget({
    super.key,
    required this.tickColor,
    this.tickBorderColor,
    this.tickBorderWidth = 0.5,
    this.tickBorderRadius,
    this.majorTickWidth = 2.0,
    this.majorTickHeightRatio = 0.4,
    this.mediumTickWidth = 1.5,
    this.mediumTickHeightRatio = 0.25,
    this.minorTickWidth = 1.0,
    this.minorTickHeightRatio = 0.15,
    required this.visibleTicks,
    this.minorTicksPerMajor = 4,
    this.majorTickInterval = 5,
    this.mediumTickInterval,
    this.minValue = -10.0,
    this.maxValue = 10.0,
    this.showLabels = true,
    this.labelStyle,
    required this.tickSpacing,
    this.scaleFactor = 1.0,
    this.labelFormatter,
    this.rulerWidthFactor = 1.0,
    this.forceFullWidth = false,
    this.referenceRange = 20.0,
    this.showBaseline = true,
    this.baselineColor,
    this.baselineThickness = 1.0,
    this.showAlternatingMajorTicks = false,
    required this.size,
  });

  @override
  Widget build(BuildContext context) {
    final height = size.height;

    // Apply scale factor to tick heights using the ratio parameters
    final longTickHeight = height * majorTickHeightRatio * scaleFactor;
    final mediumTickHeight = height * mediumTickHeightRatio * scaleFactor;
    final shortTickHeight = height * minorTickHeightRatio * scaleFactor;
    final baselineY = height * 0.9;

    // Calculate the actual width of the ruler based on the width factor
    final rulerWidth = size.width * rulerWidthFactor;

    // Calculate the tick spacing to ensure the ruler spans the specified width percentage
    double adjustedTickSpacing;
    if (forceFullWidth) {
      // When forceFullWidth is true, we use the specified reference range
      // This ensures consistent spacing regardless of the actual value range
      adjustedTickSpacing = rulerWidth / referenceRange;
    } else {
      adjustedTickSpacing = rulerWidth / (maxValue - minValue);
    }

    // Calculate the starting X position to center the ruler
    final startX = (size.width - rulerWidth) / 2;

    // Apply scale factor to text size
    final textStyle = labelStyle ??
        TextStyle(
          fontSize: height * 0.25 * scaleFactor,
          color: tickColor,
          fontWeight: FontWeight.bold,
        );

    // Calculate visible range based on viewport - use exact min/max values
    final startTick = minValue.toInt();
    final endTick = maxValue.toInt();

    // Calculate visible tick range based on screen coordinates
    final minVisibleValue = max(minValue, startTick.toDouble());
    final maxVisibleValue = min(maxValue, endTick.toDouble());

    // Pre-calculate tick properties for better performance
    final majorTickStrokeWidth = majorTickWidth * scaleFactor;
    final mediumTickStrokeWidth = mediumTickWidth * scaleFactor;
    final minorTickStrokeWidth = minorTickWidth * scaleFactor;

    // Generate tick widgets
    List<Widget> generateTicks() {
      final ticks = <Widget>[];

      // Add baseline if enabled
      if (showBaseline) {
        final baselineColor = this.baselineColor ?? tickColor;
        final baselineThickness = this.baselineThickness * scaleFactor;

        // Ensure the baseline stays within the visible area

        //TODO: update RulerWidget to use same logic for max calculations

        final maxTickWidth = max(majorTickStrokeWidth,
            max(mediumTickStrokeWidth, minorTickStrokeWidth));

        final baselineStartX = max(0.0, startX) - maxTickWidth / 2;
        final baselineEndX =
            min(size.width, startX + rulerWidth) + maxTickWidth / 2;

        // Add baseline as a positioned container
        ticks.add(
          Positioned(
            left: baselineStartX,
            top: baselineY - (baselineThickness / 2),
            child: Container(
              width: baselineEndX - baselineStartX,
              height: baselineThickness,
              color: baselineColor,
            ),
          ),
        );
      }

      // Draw major ticks within the visible range
      for (int i = minVisibleValue.floor(); i <= maxVisibleValue.ceil(); i++) {
        // Skip values outside our range
        if (i < minValue || i > maxValue) continue;

        // Calculate position
        final relativeX = (i - minValue) * adjustedTickSpacing;
        final x = startX + relativeX;

        // Skip ticks outside the visible area (with small margin for partially visible ticks)
        if (x < -10 || x > size.width + 10) continue;

        // Determine tick type
        bool isLongTick;
        bool isMediumTick;

        if (showAlternatingMajorTicks) {
          // Alternating major ticks mode
          final isMajorTick = i % majorTickInterval == 0;
          if (!isMajorTick) continue; // Skip non-major ticks

          // Alternate between long and medium ticks
          final majorTickIndex = i ~/ majorTickInterval;
          isLongTick = majorTickIndex % 2 == 0;
          isMediumTick = !isLongTick;
        } else {
          // Standard mode
          isLongTick = i % majorTickInterval == 0;

          // Use mediumTickInterval if provided, otherwise default to even numbers
          final effectiveMediumInterval = mediumTickInterval ?? 2;
          isMediumTick = !isLongTick && i % effectiveMediumInterval == 0;
        }

        // Set tick width based on tick type
        final tickWidth = isLongTick
            ? majorTickStrokeWidth
            : (isMediumTick ? mediumTickStrokeWidth : minorTickStrokeWidth);

        // Determine tick height
        final tickHeight = isLongTick
            ? longTickHeight
            : (isMediumTick ? mediumTickHeight : shortTickHeight);

        // Create tick container
        ticks.add(
          Positioned(
            left: x - (tickWidth / 2), // Center the tick at the x position
            top: baselineY - tickHeight, // Position from baseline
            child: Container(
              width: tickWidth,
              height: tickHeight,
              decoration: BoxDecoration(
                color: tickColor,
                borderRadius: BorderRadius.circular(
                  tickBorderRadius ?? (tickWidth / 2),
                ),
                border: tickBorderColor != null
                    ? Border.all(
                        color: tickBorderColor!,
                        width: tickBorderWidth,
                      )
                    : null,
              ),
            ),
          ),
        );

        // Add label for major ticks
        if (showLabels && isLongTick) {
          final labelText =
              labelFormatter != null ? labelFormatter!(i) : i.toString();
          ticks.add(
            Positioned(
              left: x - 10, // Offset to center the text approximately
              top: baselineY + 5,
              child: Text(
                labelText,
                style: textStyle,
                textAlign: TextAlign.center,
              ),
            ),
          );
        }
      }

      // Draw minor ticks between major ticks (only if not in alternating major ticks mode)
      if (!showAlternatingMajorTicks && minorTicksPerMajor > 0) {
        // Find the first visible major tick before or at minVisibleValue
        final firstMajorTick =
            (minVisibleValue ~/ majorTickInterval) * majorTickInterval;

        // Process major ticks that could have visible minor ticks
        for (int i = firstMajorTick;
            i < maxVisibleValue;
            i += majorTickInterval) {
          // Skip if outside min/max range
          if (i < minValue || i >= maxValue) continue;

          // Calculate major tick position
          final relativeMajorX = (i - minValue) * adjustedTickSpacing;
          final majorX = startX + relativeMajorX;

          // Skip if the major tick is far outside visible area (and its minor ticks would be too)
          if (majorX < -adjustedTickSpacing ||
              majorX > size.width + adjustedTickSpacing) continue;

          // Calculate minor tick spacing
          final minorTickSpacing =
              adjustedTickSpacing / (minorTicksPerMajor + 1);

          // Draw minor ticks between this major tick and the next
          for (int j = 1; j <= minorTicksPerMajor; j++) {
            final minorTickValue = i + j / (minorTicksPerMajor + 1);

            // Skip if outside boundaries
            if (minorTickValue < minValue || minorTickValue > maxValue)
              continue;

            // Calculate minor tick position
            final minorX = majorX + (j * minorTickSpacing);

            // Skip if outside visible area
            if (minorX < 0 || minorX > size.width) continue;

            // Determine if this is a medium minor tick (middle one)
            final isMinorMedium = j == (minorTicksPerMajor + 1) ~/ 2;

            // Set tick width
            final minorTickWidth =
                isMinorMedium ? mediumTickStrokeWidth : minorTickStrokeWidth;

            // Determine height
            final minorTickHeight =
                isMinorMedium ? mediumTickHeight : shortTickHeight;

            // Create minor tick container
            ticks.add(
              Positioned(
                left: minorX -
                    (minorTickWidth / 2), // Center the tick at the x position
                top: baselineY - minorTickHeight, // Position from baseline
                child: Container(
                  width: minorTickWidth,
                  height: minorTickHeight,
                  decoration: BoxDecoration(
                    color: tickColor,
                    borderRadius: BorderRadius.circular(
                      tickBorderRadius ?? (minorTickWidth / 2),
                    ),
                    border: tickBorderColor != null
                        ? Border.all(
                            color: tickBorderColor!,
                            width: tickBorderWidth,
                          )
                        : null,
                  ),
                ),
              ),
            );
          }
        }
      }

      return ticks;
    }

    return Stack(
      clipBehavior: Clip.none,
      children: generateTicks(),
    );
  }
}

/// Custom painter for drawing the fixed ruler with moving pointer
/// This is kept for backward compatibility but delegates to FixedRulerWidget
class FixedRulerPainter extends CustomPainter {
  final Color tickColor;
  final Color? tickBorderColor;
  final double tickBorderWidth;
  final double? tickBorderRadius;
  final double majorTickWidth;
  final double majorTickHeightRatio;
  final double mediumTickWidth;
  final double mediumTickHeightRatio;
  final double minorTickWidth;
  final double minorTickHeightRatio;
  final int visibleTicks;
  final int minorTicksPerMajor;
  final int majorTickInterval;
  final int? mediumTickInterval;
  final double minValue;
  final double maxValue;
  final bool showLabels;
  final TextStyle? labelStyle;
  final double tickSpacing;
  final double scaleFactor;
  final String Function(int value)? labelFormatter;
  final double rulerWidthFactor;

  final bool forceFullWidth;
  final double referenceRange;
  final bool showBaseline;
  final Color? baselineColor;
  final double baselineThickness;
  final bool showAlternatingMajorTicks;

  FixedRulerPainter({
    required this.tickColor,
    this.tickBorderColor,
    this.tickBorderWidth = 0.5,
    this.tickBorderRadius,
    this.majorTickWidth = 2.0,
    this.majorTickHeightRatio = 0.4,
    this.mediumTickWidth = 1.5,
    this.mediumTickHeightRatio = 0.25,
    this.minorTickWidth = 1.0,
    this.minorTickHeightRatio = 0.15,
    required this.visibleTicks,
    this.minorTicksPerMajor = 4,
    this.majorTickInterval = 5,
    this.mediumTickInterval,
    this.minValue = -10.0,
    this.maxValue = 10.0,
    this.showLabels = true,
    this.labelStyle,
    required this.tickSpacing,
    this.scaleFactor = 1.0,
    this.labelFormatter,
    this.rulerWidthFactor = 1.0,
    this.forceFullWidth = false,
    this.referenceRange = 20.0,
    this.showBaseline = true,
    this.baselineColor,
    this.baselineThickness = 1.0,
    this.showAlternatingMajorTicks = false,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // This is now just a stub that renders nothing
    // The actual rendering is done by FixedRulerWidget
  }

  @override
  bool shouldRepaint(FixedRulerPainter oldDelegate) {
    // Only check properties that would visually affect the painting
    return oldDelegate.tickColor != tickColor ||
        oldDelegate.tickBorderColor != tickBorderColor ||
        oldDelegate.tickBorderWidth != tickBorderWidth ||
        oldDelegate.tickBorderRadius != tickBorderRadius ||
        oldDelegate.majorTickWidth != majorTickWidth ||
        oldDelegate.majorTickHeightRatio != majorTickHeightRatio ||
        oldDelegate.mediumTickWidth != mediumTickWidth ||
        oldDelegate.mediumTickHeightRatio != mediumTickHeightRatio ||
        oldDelegate.minorTickWidth != minorTickWidth ||
        oldDelegate.minorTickHeightRatio != minorTickHeightRatio ||
        oldDelegate.showLabels != showLabels ||
        oldDelegate.showBaseline != showBaseline ||
        oldDelegate.showAlternatingMajorTicks != showAlternatingMajorTicks ||
        oldDelegate.tickSpacing != tickSpacing ||
        // Check dimension-related properties
        oldDelegate.visibleTicks != visibleTicks ||
        oldDelegate.minorTicksPerMajor != minorTicksPerMajor ||
        oldDelegate.majorTickInterval != majorTickInterval ||
        oldDelegate.mediumTickInterval != mediumTickInterval ||
        oldDelegate.minValue != minValue ||
        oldDelegate.maxValue != maxValue ||
        oldDelegate.scaleFactor != scaleFactor ||
        oldDelegate.rulerWidthFactor != rulerWidthFactor ||
        oldDelegate.forceFullWidth != forceFullWidth ||
        oldDelegate.referenceRange != referenceRange ||
        // Check style-related properties
        oldDelegate.baselineColor != baselineColor ||
        oldDelegate.baselineThickness != baselineThickness;
  }
}

/// Custom painter for drawing the arrow-shaped pointer marker
class PointerMarkerPainter extends CustomPainter {
  final Color color;

  PointerMarkerPainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final w = size.width;
    final h = size.height;

    // Adjust dimensions based on width for better appearance at small sizes
    final arrowH = h * (w < 10 ? 0.2 : 0.3);
    final boxH = h - arrowH;
    final r = (w < 10 ? 1.0 : 2.0); // Corner radius

    // Create arrow path
    final path = Path();
    // Start at top‑left corner (inset by r)
    path.moveTo(r, 0);
    // Top edge
    path.lineTo(w - r, 0);
    // Top‑right corner
    path.quadraticBezierTo(w, 0, w, r);
    // Right edge down to just above bottom‑corner fillet
    path.lineTo(w, boxH - r);
    // Bottom‑right box corner (fillet)
    path.quadraticBezierTo(w, boxH, w - r, boxH);
    // Arrow tip
    path.lineTo(w / 2, h);
    // Bottom‑left box corner (fillet)
    path.lineTo(r, boxH);
    path.quadraticBezierTo(0, boxH, 0, boxH - r);
    // Left edge up to top‑left fillet start
    path.lineTo(0, r);
    // Top‑left corner
    path.quadraticBezierTo(0, 0, r, 0);
    path.close();

    // Fill shape
    canvas.drawPath(path, paint);

    // Stroke border with slightly transparent version of the same color
    paint
      ..style = PaintingStyle.stroke
      ..strokeWidth = w < 10 ? 0.5 : 1.0
      ..color = color.withAlpha(204); // 0.8 opacity (204/255)
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant PointerMarkerPainter old) => old.color != color;
}
