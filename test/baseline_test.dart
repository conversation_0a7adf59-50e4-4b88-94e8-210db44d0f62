import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import '../lib/global/precision_ruler_widget.dart';

void main() {
  testWidgets('PrecisionRuler baseline extends to tick edges', (WidgetTester tester) async {
    // Test with wider ticks to verify baseline extends properly
    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: Center(
            child: SizedBox(
              width: 300,
              height: 100,
              child: PrecisionRuler(
                value: 0.0,
                minValue: -5.0,
                maxValue: 5.0,
                majorTickWidth: 5.0, // Wide ticks to test baseline extension
                mediumTickWidth: 3.0,
                minorTickWidth: 2.0,
                showBaseline: true,
                tickColor: Colors.black,
                baselineColor: Colors.red,
                onChanged: (value) {},
              ),
            ),
          ),
        ),
      ),
    );

    // Verify the widget renders without errors
    expect(find.byType(PrecisionRuler), findsOneWidget);
    
    // The test passes if no exceptions are thrown during rendering
    await tester.pumpAndSettle();
  });

  testWidgets('FixedRulerWidget baseline extends to tick edges', (WidgetTester tester) async {
    // Test FixedRulerWidget with wider ticks
    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: Center(
            child: SizedBox(
              width: 300,
              height: 100,
              child: PrecisionRuler(
                value: 0.0,
                minValue: -5.0,
                maxValue: 5.0,
                majorTickWidth: 4.0, // Wide ticks
                mediumTickWidth: 3.0,
                minorTickWidth: 2.0,
                showBaseline: true,
                tickColor: Colors.black,
                baselineColor: Colors.blue,
                movePointer: false, // This uses FixedRulerWidget
                onChanged: (value) {},
              ),
            ),
          ),
        ),
      ),
    );

    // Verify the widget renders without errors
    expect(find.byType(PrecisionRuler), findsOneWidget);
    
    // The test passes if no exceptions are thrown during rendering
    await tester.pumpAndSettle();
  });
}
