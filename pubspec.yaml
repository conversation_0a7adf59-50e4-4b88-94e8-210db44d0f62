name: fujimo
description: "A retro film camera app for mobile"

publish_to: "none"

version: 1.0.0+1

environment:
  sdk: ">=3.4.3 <4.0.0"

dependencies:
  flutter:
    sdk: flutter

  cupertino_icons: ^1.0.6
  camera: ^0.11.0+2

  dartz: ^0.10.1
  flutter_svg: ^2.0.10+1
  flutter_animate: ^4.5.0
  audioplayers: ^6.4.0
  flutter_inner_shadow: ^0.0.1
  get_it: ^8.0.0
  flutter_bloc: ^8.1.6
  battery_plus: ^6.2.1
  haptic_feedback: ^0.5.1+1
  image: ^4.5.4
  path_provider: ^2.1.5
  #opencv_dart: ^1.4.1

dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^3.0.0

flutter:
  uses-material-design: true

  assets:
    - assets/camera-components/
    - assets/audio/
    - assets/fonts/

  fonts:
    - family: DSDigital
      fonts:
        - asset: assets/fonts/DS-DIGI.TTF
    - family: DSDigitalBold
      fonts:
        - asset: assets/fonts/DS-DIGIB.TTF
    - family: DSDigitalItalic
      fonts:
        - asset: assets/fonts/DS-DIGII.TTF
    - family: DSDigitalThin
      fonts:
        - asset: assets/fonts/DS-DIGIT.TTF

    - family: Oxanium
      fonts:
        - asset: assets/fonts/Oxanium.TTF
